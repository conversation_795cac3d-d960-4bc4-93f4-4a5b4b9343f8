import { faker } from '@faker-js/faker';
import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, usePageResponseSuccess } from '~/utils/response';

const formatterCN = new Intl.DateTimeFormat('zh-CN', {
  timeZone: 'Asia/Shanghai',
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
});

const feedbackTitles = [
  '产品使用问题反馈',
  '界面显示异常',
  '功能建议',
  '系统响应缓慢',
  '数据同步问题',
  '账户登录困难',
  '支付流程优化建议',
  '用户体验改进意见',
  '新功能需求',
  'Bug报告',
];

const feedbackContents = [
  '在使用过程中发现页面加载速度较慢，希望能够优化。',
  '移动端界面在某些机型上显示不完整，建议适配。',
  '希望增加批量操作功能，提高工作效率。',
  '系统在高峰期响应时间过长，影响正常使用。',
  '数据同步存在延迟，希望能够实时更新。',
  '登录页面偶尔出现验证码显示不清楚的问题。',
  '支付流程步骤较多，建议简化操作流程。',
  '整体界面设计不错，但某些按钮位置可以优化。',
  '建议增加夜间模式，方便夜晚使用。',
  '发现在特定操作下会出现页面卡死的情况。',
];

function generateMockDataList(count: number) {
  const dataList = [];

  for (let i = 0; i < count; i++) {
    const dataItem: Record<string, any> = {
      id: faker.string.uuid(),
      feedbackCode: `FB${faker.string.numeric(8)}`,
      userId: `U${faker.string.numeric(8)}`,
      username: faker.internet.username(),
      feedbackTitle: faker.helpers.arrayElement(feedbackTitles),
      feedbackContent: faker.helpers.arrayElement(feedbackContents),
      status: faker.helpers.arrayElement([0, 1, 2]), // 0: 待处理, 1: 已处理, 2: 已关闭
      createTime: formatterCN.format(
        faker.date.between({ from: '2022-01-01', to: '2025-01-01' }),
      ),
    };

    dataList.push(dataItem);
  }

  return dataList;
}

const mockData = generateMockDataList(150);

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const {
    page = 1,
    pageSize = 20,
    feedbackCode,
    userId,
    username,
    status,
    startTime,
    endTime,
  } = getQuery(event);

  let listData = structuredClone(mockData);

  if (feedbackCode) {
    listData = listData.filter((item) =>
      item.feedbackCode
        .toLowerCase()
        .includes(String(feedbackCode).toLowerCase()),
    );
  }

  if (userId) {
    listData = listData.filter((item) =>
      item.userId.toLowerCase().includes(String(userId).toLowerCase()),
    );
  }

  if (username) {
    listData = listData.filter((item) =>
      item.username.toLowerCase().includes(String(username).toLowerCase()),
    );
  }

  if (['0', '1', '2'].includes(status as string)) {
    listData = listData.filter((item) => item.status === Number(status));
  }

  if (startTime) {
    listData = listData.filter((item) => item.createTime >= startTime);
  }

  if (endTime) {
    listData = listData.filter((item) => item.createTime <= endTime);
  }

  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
