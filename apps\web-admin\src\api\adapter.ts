/**
 * API 接口适配器
 * 用于适配后台接口的数据格式和前端需求
 */

import type { PageFetchParams } from './request';

/**
 * 通用分页参数转换
 * 将前端分页参数转换为后台接口需要的格式
 * 根据接口文档，后台使用 page 和 page_size 参数
 */
export function transformPageParams(params: PageFetchParams) {
  const { pageNo = 1, pageSize = 20, ...rest } = params;

  // 根据接口文档的参数格式
  return {
    page: pageNo,           // 页码，默认1
    page_size: pageSize,    // 每页条数，默认20
    ...rest,
  };
}

/**
 * 通用分页响应数据转换
 * 将后台返回的分页数据转换为前端需要的格式
 * 根据接口文档，后台返回格式为 {total, page, page_size, list}
 */
export function transformPageResponse(response: any) {
  // 根据接口文档的响应格式
  return {
    items: response.list || [],           // 数据列表
    total: response.total || 0,           // 总数
    page: response.page || 1,             // 当前页
    size: response.page_size || 20,       // 每页大小
  };
}

/**
 * 通用响应数据转换
 * 统一处理后台返回的数据格式
 * 根据接口文档，成功状态码为0，失败为非0
 */
export function transformResponse<T = any>(response: any): T {
  // 根据接口文档的响应格式 {code, message, data}
  if (response.code === 0) {
    return response.data;
  }

  // 根据状态码提供更详细的错误信息
  const errorMessages: Record<number, string> = {
    1001: '参数错误',
    1002: '权限不足',
    1003: '资源不存在',
    1004: '操作失败',
    1005: '服务器内部错误',
    2001: '用户未登录',
    2002: '登录失败',
    2003: '用户不存在',
    3001: '数据库操作错误',
  };

  const errorMessage = errorMessages[response.code] || response.message || '请求失败';
  throw new Error(errorMessage);
}

/**
 * 文件上传参数转换
 */
export function transformUploadParams(file: File, additionalParams?: Record<string, any>) {
  const formData = new FormData();
  formData.append('file', file);
  
  if (additionalParams) {
    Object.keys(additionalParams).forEach(key => {
      formData.append(key, additionalParams[key]);
    });
  }
  
  return formData;
}

/**
 * 日期范围参数转换
 */
export function transformDateRangeParams(dateRange: [string, string] | null, startField = 'startTime', endField = 'endTime') {
  if (!dateRange || dateRange.length !== 2) {
    return {};
  }
  
  return {
    [startField]: dateRange[0],
    [endField]: dateRange[1],
  };
}

/**
 * 状态值转换
 * 将前端的状态值转换为后台需要的格式
 */
export function transformStatus(status: string | number) {
  // 根据您的后台接口调整状态值映射
  const statusMap: Record<string, any> = {
    'active': 1,
    'inactive': 0,
    'enabled': 1,
    'disabled': 0,
    'pending': 0,
    'approved': 1,
    'rejected': 2,
  };
  
  return statusMap[status] ?? status;
}

/**
 * 排序参数转换
 */
export function transformSortParams(sorter: any) {
  if (!sorter || !sorter.field) {
    return {};
  }
  
  return {
    sortField: sorter.field,
    sortOrder: sorter.order === 'descend' ? 'desc' : 'asc',
  };
}
