import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'mdi:package-variant',
      order: 2000,
      title: $t('product.title'),
    },
    name: 'Product',
    path: '/product',
    // redirect: '#/views/product/list.vue',
    component: () => import('#/views/product/list.vue'),
    // children: [
    //   {
    //     path: '/product/list',
    //     name: 'ProductList',
    //     meta: {
    //       icon: 'mdi:format-list-bulleted',
    //       title: $t('product.list'),
    //     },
    //     component: () => import('#/views/product/list.vue'),
    //   },
    // ],
  },
];

export default routes;
