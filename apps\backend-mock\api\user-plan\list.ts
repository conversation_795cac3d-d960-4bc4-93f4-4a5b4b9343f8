import { faker } from '@faker-js/faker';
import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, usePageResponseSuccess } from '~/utils/response';

const formatterCN = new Intl.DateTimeFormat('zh-CN', {
  timeZone: 'Asia/Shanghai',
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
});

const userNicknames = [
  '小明',
  '小红',
  '小李',
  '小王',
  '小张',
  '小刘',
  '小陈',
  '小杨',
  '小赵',
  '小孙',
  '阿强',
  '阿华',
  '阿伟',
  '阿军',
  '阿峰',
  '阿涛',
  '阿斌',
  '阿超',
  '阿龙',
  '阿飞',
  '晓雯',
  '晓敏',
  '晓丽',
  '晓燕',
  '晓霞',
  '晓琳',
  '晓娟',
  '晓芳',
  '晓慧',
  '晓玲',
  '投放专家',
  '广告达人',
  '营销高手',
  '推广能手',
  '流量大师',
];

const launchStatusTypes = [
  'pending',
  'launching',
  'completed',
  'paused',
  'failed',
];

function generateMockDataList(count: number) {
  const dataList = [];

  for (let i = 0; i < count; i++) {
    const createTime = faker.date.between({
      from: '2022-01-01',
      to: '2025-01-01',
    });
    const updateTime = faker.date.between({
      from: createTime,
      to: '2025-01-01',
    });

    // 生成投放相关数据，确保逻辑合理
    const launchAmount = faker.number.float({
      min: 100,
      max: 10_000,
      fractionDigits: 2,
    });
    const launchProgress = faker.number.float({
      min: 0,
      max: 100,
      fractionDigits: 2,
    });
    const launched = (launchAmount * launchProgress) / 100;
    const pending = launchAmount - launched;

    const dataItem: Record<string, any> = {
      id: faker.string.uuid(),
      groupPlanId: `GP${faker.string.numeric(8)}`,
      userId: `U${faker.string.numeric(8)}`,
      userNickname: faker.helpers.arrayElement(userNicknames),
      orderNumber: `ORD${faker.string.numeric(12)}`,
      groupOrder: faker.number.int({ min: 1, max: 20 }),
      launchAmount,
      launchProgress,
      launched,
      pending,
      launchStatus: faker.helpers.arrayElement(launchStatusTypes),
      clickPrice: faker.number.float({ min: 0.1, max: 50, fractionDigits: 4 }),
      updateTime: formatterCN.format(updateTime),
      createTime: formatterCN.format(createTime),
    };

    dataList.push(dataItem);
  }

  return dataList;
}

const mockData = generateMockDataList(90);

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const {
    page = 1,
    pageSize = 20,
    groupPlanId,
    userId,
    userNickname,
    orderNumber,
    launchStatus,
    startTime,
    endTime,
  } = getQuery(event);

  let listData = structuredClone(mockData);

  if (groupPlanId) {
    listData = listData.filter((item) =>
      item.groupPlanId
        .toLowerCase()
        .includes(String(groupPlanId).toLowerCase()),
    );
  }

  if (userId) {
    listData = listData.filter((item) =>
      item.userId.toLowerCase().includes(String(userId).toLowerCase()),
    );
  }

  if (userNickname) {
    listData = listData.filter((item) =>
      item.userNickname
        .toLowerCase()
        .includes(String(userNickname).toLowerCase()),
    );
  }

  if (orderNumber) {
    listData = listData.filter((item) =>
      item.orderNumber
        .toLowerCase()
        .includes(String(orderNumber).toLowerCase()),
    );
  }

  if (launchStatus) {
    listData = listData.filter((item) => item.launchStatus === launchStatus);
  }

  if (startTime) {
    listData = listData.filter((item) => item.updateTime >= startTime);
  }

  if (endTime) {
    listData = listData.filter((item) => item.updateTime <= endTime);
  }

  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
