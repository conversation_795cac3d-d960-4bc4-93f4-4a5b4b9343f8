import { faker } from '@faker-js/faker';
import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, usePageResponseSuccess } from '~/utils/response';

const formatterCN = new Intl.DateTimeFormat('zh-CN', {
  timeZone: 'Asia/Shanghai',
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
});

const userNicknames = [
  '小明',
  '小红',
  '小李',
  '小王',
  '小张',
  '小刘',
  '小陈',
  '小杨',
  '小赵',
  '小孙',
  '阿强',
  '阿华',
  '阿伟',
  '阿军',
  '阿峰',
  '阿涛',
  '阿斌',
  '阿超',
  '阿龙',
  '阿飞',
  '晓雯',
  '晓敏',
  '晓丽',
  '晓燕',
  '晓霞',
  '晓琳',
  '晓娟',
  '晓芳',
  '晓慧',
  '晓玲',
];

const rechargeTypes = ['manual', 'auto', 'bonus', 'refund'];
const rechargeMethods = ['alipay', 'wechat', 'bank', 'crypto'];
const statusTypes = [
  'pending',
  'processing',
  'completed',
  'failed',
  'cancelled',
];

function generateMockDataList(count: number) {
  const dataList = [];

  for (let i = 0; i < count; i++) {
    const createTime = faker.date.between({
      from: '2022-01-01',
      to: '2025-01-01',
    });
    const updateTime = faker.date.between({
      from: createTime,
      to: '2025-01-01',
    });

    const dataItem: Record<string, any> = {
      id: faker.string.uuid(),
      userId: `U${faker.string.numeric(8)}`,
      userNickname: faker.helpers.arrayElement(userNicknames),
      fbId: `FB${faker.string.numeric(10)}`,
      floatingAmount: faker.number.float({
        min: 10,
        max: 10_000,
        fractionDigits: 2,
      }),
      rechargeType: faker.helpers.arrayElement(rechargeTypes),
      rechargeAmount: faker.number.float({
        min: 50,
        max: 5000,
        fractionDigits: 2,
      }),
      rechargeMethod: faker.helpers.arrayElement(rechargeMethods),
      status: faker.helpers.arrayElement(statusTypes),
      virtualCoinRecovery: faker.helpers.maybe(
        () => faker.number.float({ min: 0, max: 100, fractionDigits: 2 }),
        { probability: 0.3 },
      ),
      adminNote: faker.helpers.maybe(() => faker.lorem.sentence(), {
        probability: 0.4,
      }),
      userNote: faker.helpers.maybe(() => faker.lorem.sentence(), {
        probability: 0.3,
      }),
      updateTime: formatterCN.format(updateTime),
      createTime: formatterCN.format(createTime),
    };

    dataList.push(dataItem);
  }

  return dataList;
}

const mockData = generateMockDataList(80);

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const {
    page = 1,
    pageSize = 20,
    userId,
    userNickname,
    fbId,
    rechargeType,
    rechargeMethod,
    status,
    startTime,
    endTime,
  } = getQuery(event);

  let listData = structuredClone(mockData);

  if (userId) {
    listData = listData.filter((item) =>
      item.userId.toLowerCase().includes(String(userId).toLowerCase()),
    );
  }

  if (userNickname) {
    listData = listData.filter((item) =>
      item.userNickname
        .toLowerCase()
        .includes(String(userNickname).toLowerCase()),
    );
  }

  if (fbId) {
    listData = listData.filter((item) =>
      item.fbId.toLowerCase().includes(String(fbId).toLowerCase()),
    );
  }

  if (rechargeType) {
    listData = listData.filter((item) => item.rechargeType === rechargeType);
  }

  if (rechargeMethod) {
    listData = listData.filter(
      (item) => item.rechargeMethod === rechargeMethod,
    );
  }

  if (status) {
    listData = listData.filter((item) => item.status === status);
  }

  if (startTime) {
    listData = listData.filter((item) => item.createTime >= startTime);
  }

  if (endTime) {
    listData = listData.filter((item) => item.createTime <= endTime);
  }

  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
