# 后台接口对接指南

## 🚀 快速开始

### 1. 环境切换（三种方式）

#### 方式一：使用切换脚本（推荐）
```bash
# 切换到Mock模式（用于开发调试）
node switch-env.js mock

# 切换到真实后台模式
node switch-env.js real http://localhost:8080/api
```

#### 方式二：使用浏览器控制台
启动项目后，在浏览器控制台运行：
```javascript
// 查看当前环境信息
EnvSwitcher.getEnvInfo()

// 显示切换指南
EnvSwitcher.showSwitchGuide()

// 测试后台连通性
EnvSwitcher.checkBackendConnection()
```

#### 方式三：手动编辑配置文件
编辑 `apps/web-admin/.env.development`：
```env
# Mock模式（开发调试）
VITE_GLOB_API_URL=/api
VITE_NITRO_MOCK=true

# 真实后台模式
# VITE_GLOB_API_URL=http://localhost:8080/api
# VITE_NITRO_MOCK=false
```

#### 生产环境
编辑 `apps/web-admin/.env.production`：
```env
# 接口地址 - 替换为您的生产环境后台接口地址
VITE_GLOB_API_URL=https://your-production-api.com/api
```

### 2. 后台接口要求

#### 响应数据格式
```json
{
  "code": 0,           // 成功状态码，可在 request.ts 中配置
  "data": {},          // 实际数据
  "message": "success" // 消息
}
```

#### 分页数据格式
```json
{
  "code": 0,
  "data": {
    "records": [],     // 数据列表，可以是 data/list/items
    "total": 100,      // 总数，可以是 totalCount
    "current": 1,      // 当前页，可以是 page/pageNo
    "size": 10         // 每页大小，可以是 pageSize/limit
  }
}
```

#### 认证相关
- 登录接口：`POST /auth/login`
- 刷新Token：`POST /auth/refresh`
- 退出登录：`POST /auth/logout`
- 获取权限码：`GET /auth/codes`

### 3. 使用 BaseApiService

```typescript
import { BaseApiService } from '#/api/base';

// 创建API服务实例
const userApiService = new BaseApiService('/user');

// 获取列表
const userList = await userApiService.getList({ page: 1, size: 10 });

// 获取详情
const userDetail = await userApiService.getDetail(userId);

// 创建
const newUser = await userApiService.create(userData);

// 更新
const updatedUser = await userApiService.update(userId, userData);

// 删除
await userApiService.delete(userId);
```

### 4. 自定义API适配

如果您的后台接口格式与默认格式不同，请修改 `apps/web-admin/src/api/adapter.ts`：

```typescript
// 修改分页参数转换
export function transformPageParams(params: PageFetchParams) {
  const { pageNo = 1, pageSize = 10, ...rest } = params;
  
  return {
    pageNum: pageNo,    // 改为您的后台参数名
    limit: pageSize,    // 改为您的后台参数名
    ...rest,
  };
}

// 修改响应数据转换
export function transformPageResponse(response: any) {
  return {
    items: response.list,        // 改为您的后台字段名
    total: response.totalCount,  // 改为您的后台字段名
    page: response.pageNum,      // 改为您的后台字段名
    size: response.limit,        // 改为您的后台字段名
  };
}
```

### 5. 请求拦截器配置

在 `apps/web-admin/src/api/request.ts` 中已配置：
- 自动添加 Authorization 头
- 自动添加语言头
- Token 过期自动刷新
- 统一错误处理

### 6. 跨域配置

如果遇到跨域问题，在 `apps/web-admin/vite.config.mts` 中配置代理：

```typescript
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:8080',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api/, '/api')
    }
  }
}
```

## 📝 接口对接清单

### 核心接口
- [ ] 登录接口 `/auth/login`
- [ ] 刷新Token `/auth/refresh`
- [ ] 退出登录 `/auth/logout`
- [ ] 获取用户信息 `/auth/user`
- [ ] 获取权限码 `/auth/codes`
- [ ] 获取菜单 `/auth/menu`

### 业务接口
- [ ] 用户管理 `/user/*`
- [ ] 产品管理 `/product/*`
- [ ] 订单管理 `/plan/*`
- [ ] 优惠券管理 `/coupon/*`
- [ ] 财务管理 `/finance/*`
- [ ] VIP管理 `/vip/*`
- [ ] 代理管理 `/agency/*`
- [ ] 反馈管理 `/feedback/*`
- [ ] 统计数据 `/statistics/*`

## 🔧 调试技巧

1. 打开浏览器开发者工具的 Network 面板查看请求
2. 检查请求头是否正确
3. 检查响应数据格式是否符合预期
4. 使用 Postman 等工具测试后台接口
5. 查看控制台错误信息

## ⚠️ 注意事项

1. 确保后台接口支持 CORS
2. 确保 Token 格式正确（默认使用 Bearer Token）
3. 确保响应数据格式统一
4. 建议使用 HTTPS 协议
5. 注意接口版本管理
