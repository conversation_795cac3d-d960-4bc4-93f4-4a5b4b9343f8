import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, useResponseSuccess } from '~/utils/response';

const formatterCN = new Intl.DateTimeFormat('zh-CN', {
  timeZone: 'Asia/Shanghai',
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
});

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const id = getRouterParam(event, 'id');
  const body = await readBody(event);

  // 模拟更新用户计划
  const updatedUserPlan = {
    id,
    ...body,
    updateTime: formatterCN.format(new Date()),
  };

  // 模拟延迟
  await new Promise((resolve) => setTimeout(resolve, 500));

  return useResponseSuccess(updatedUserPlan);
});
