/**
 * 环境切换工具
 * 用于在Mock模式和真实后台之间快速切换
 */

export class EnvSwitcher {
  /**
   * 检查当前是否为Mock模式
   */
  static isMockMode(): boolean {
    return import.meta.env.VITE_NITRO_MOCK === 'true';
  }

  /**
   * 获取当前API地址
   */
  static getCurrentApiUrl(): string {
    return import.meta.env.VITE_GLOB_API_URL;
  }

  /**
   * 获取环境信息
   */
  static getEnvInfo() {
    const info = {
      mode: import.meta.env.MODE,
      isMock: this.isMockMode(),
      apiUrl: this.getCurrentApiUrl(),
      isDev: import.meta.env.DEV,
      isProd: import.meta.env.PROD,
    };

    console.log('🌍 当前环境信息:', info);
    return info;
  }

  /**
   * 显示切换指南
   */
  static showSwitchGuide() {
    console.log(`
🔄 环境切换指南:

📋 当前状态:
- Mock模式: ${this.isMockMode() ? '✅ 开启' : '❌ 关闭'}
- API地址: ${this.getCurrentApiUrl()}

🚀 切换到真实后台:
1. 编辑 apps/web-admin/.env.development
2. 修改配置:
   VITE_GLOB_API_URL=http://您的后台地址:端口/api
   VITE_NITRO_MOCK=false
3. 重启开发服务器

🔙 切换回Mock模式:
1. 编辑 apps/web-admin/.env.development
2. 修改配置:
   VITE_GLOB_API_URL=/api
   VITE_NITRO_MOCK=true
3. 重启开发服务器

💡 提示: 修改 .env 文件后需要重启开发服务器才能生效
    `);
  }

  /**
   * 生成配置文件内容
   */
  static generateConfig(mode: 'mock' | 'real', realApiUrl?: string) {
    if (mode === 'mock') {
      return `# Mock模式配置
VITE_GLOB_API_URL=/api
VITE_NITRO_MOCK=true`;
    } else {
      const apiUrl = realApiUrl || 'http://localhost:8080/api';
      return `# 真实后台配置
VITE_GLOB_API_URL=${apiUrl}
VITE_NITRO_MOCK=false`;
    }
  }

  /**
   * 检查后台连通性
   */
  static async checkBackendConnection(apiUrl?: string) {
    const testUrl = apiUrl || this.getCurrentApiUrl();
    
    try {
      // 简单的连通性测试
      const response = await fetch(`${testUrl}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        console.log('✅ 后台连通性测试成功');
        return { success: true, status: response.status };
      } else {
        console.log('⚠️ 后台响应异常:', response.status);
        return { success: false, status: response.status };
      }
    } catch (error) {
      console.log('❌ 后台连通性测试失败:', error);
      return { success: false, error };
    }
  }
}

// 在开发环境下自动暴露到全局
if (import.meta.env.DEV) {
  (window as any).EnvSwitcher = EnvSwitcher;
  
  // 自动显示环境信息
  console.log('🔧 环境切换工具已加载');
  EnvSwitcher.getEnvInfo();
  
  console.log(`
💡 快速命令:
- EnvSwitcher.getEnvInfo() - 查看环境信息
- EnvSwitcher.showSwitchGuide() - 显示切换指南
- EnvSwitcher.checkBackendConnection() - 测试后台连通性
  `);
}
