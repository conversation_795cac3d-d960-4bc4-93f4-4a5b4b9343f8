import { faker } from '@faker-js/faker';
import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, usePageResponseSuccess } from '~/utils/response';

const formatterCN = new Intl.DateTimeFormat('zh-CN', {
  timeZone: 'Asia/Shanghai',
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
});

const userNicknames = [
  '小明',
  '小红',
  '小李',
  '小王',
  '小张',
  '小刘',
  '小陈',
  '小杨',
  '小赵',
  '小孙',
  '阿强',
  '阿华',
  '阿伟',
  '阿军',
  '阿峰',
  '阿涛',
  '阿斌',
  '阿超',
  '阿龙',
  '阿飞',
  '晓雯',
  '晓敏',
  '晓丽',
  '晓燕',
  '晓霞',
  '晓琳',
  '晓娟',
  '晓芳',
  '晓慧',
  '晓玲',
  '代运营小助手',
  '营销专家',
  '推广达人',
  '运营高手',
  '流量大师',
];

function generateMockDataList(count: number) {
  const dataList = [];

  for (let i = 0; i < count; i++) {
    const createTime = faker.date.between({
      from: '2022-01-01',
      to: '2025-01-01',
    });
    const updateTime = faker.date.between({
      from: createTime,
      to: '2025-01-01',
    });

    const dataItem: Record<string, any> = {
      id: faker.string.uuid(),
      userId: `U${faker.string.numeric(8)}`,
      userNickname: faker.helpers.arrayElement(userNicknames),
      agencyId: `A${faker.string.numeric(8)}`,
      followedUserId: `FU${faker.string.numeric(8)}`,
      followedAgencyId: `FA${faker.string.numeric(8)}`,
      status: faker.helpers.arrayElement([0, 1]),
      updateTime: formatterCN.format(updateTime),
      createTime: formatterCN.format(createTime),
    };

    dataList.push(dataItem);
  }

  return dataList;
}

const mockData = generateMockDataList(70);

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const {
    page = 1,
    pageSize = 20,
    userId,
    userNickname,
    agencyId,
    followedUserId,
    followedAgencyId,
    status,
    startTime,
    endTime,
    updateStartTime,
    updateEndTime,
  } = getQuery(event);

  let listData = structuredClone(mockData);

  if (userId) {
    listData = listData.filter((item) =>
      item.userId.toLowerCase().includes(String(userId).toLowerCase()),
    );
  }

  if (userNickname) {
    listData = listData.filter((item) =>
      item.userNickname
        .toLowerCase()
        .includes(String(userNickname).toLowerCase()),
    );
  }

  if (agencyId) {
    listData = listData.filter((item) =>
      item.agencyId.toLowerCase().includes(String(agencyId).toLowerCase()),
    );
  }

  if (followedUserId) {
    listData = listData.filter((item) =>
      item.followedUserId
        .toLowerCase()
        .includes(String(followedUserId).toLowerCase()),
    );
  }

  if (followedAgencyId) {
    listData = listData.filter((item) =>
      item.followedAgencyId
        .toLowerCase()
        .includes(String(followedAgencyId).toLowerCase()),
    );
  }

  if (['0', '1'].includes(status as string)) {
    listData = listData.filter((item) => item.status === Number(status));
  }

  if (startTime) {
    listData = listData.filter((item) => item.createTime >= startTime);
  }

  if (endTime) {
    listData = listData.filter((item) => item.createTime <= endTime);
  }

  if (updateStartTime) {
    listData = listData.filter((item) => item.updateTime >= updateStartTime);
  }

  if (updateEndTime) {
    listData = listData.filter((item) => item.updateTime <= updateEndTime);
  }

  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
