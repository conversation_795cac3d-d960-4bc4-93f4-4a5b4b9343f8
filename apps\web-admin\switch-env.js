#!/usr/bin/env node

/**
 * 环境切换脚本
 * 用于快速在Mock模式和真实后台之间切换
 * 
 * 使用方法:
 * node switch-env.js mock                           # 切换到Mock模式
 * node switch-env.js real http://localhost:8080/api # 切换到真实后台
 */

const fs = require('fs');
const path = require('path');

const envFile = path.join(__dirname, '.env.development');

function readEnvFile() {
  if (!fs.existsSync(envFile)) {
    console.error('❌ .env.development 文件不存在');
    process.exit(1);
  }
  return fs.readFileSync(envFile, 'utf8');
}

function writeEnvFile(content) {
  fs.writeFileSync(envFile, content, 'utf8');
}

function switchToMock() {
  let content = readEnvFile();
  
  // 更新API地址
  content = content.replace(
    /VITE_GLOB_API_URL=.*/,
    'VITE_GLOB_API_URL=/api'
  );
  
  // 开启Mock服务
  content = content.replace(
    /VITE_NITRO_MOCK=.*/,
    'VITE_NITRO_MOCK=true'
  );
  
  writeEnvFile(content);
  
  console.log('✅ 已切换到Mock模式');
  console.log('📋 当前配置:');
  console.log('   VITE_GLOB_API_URL=/api');
  console.log('   VITE_NITRO_MOCK=true');
  console.log('🔄 请重启开发服务器以使配置生效');
}

function switchToReal(apiUrl) {
  if (!apiUrl) {
    console.error('❌ 请提供真实后台API地址');
    console.log('💡 示例: node switch-env.js real http://localhost:8080/api');
    process.exit(1);
  }
  
  let content = readEnvFile();
  
  // 更新API地址
  content = content.replace(
    /VITE_GLOB_API_URL=.*/,
    `VITE_GLOB_API_URL=${apiUrl}`
  );
  
  // 关闭Mock服务
  content = content.replace(
    /VITE_NITRO_MOCK=.*/,
    'VITE_NITRO_MOCK=false'
  );
  
  writeEnvFile(content);
  
  console.log('✅ 已切换到真实后台模式');
  console.log('📋 当前配置:');
  console.log(`   VITE_GLOB_API_URL=${apiUrl}`);
  console.log('   VITE_NITRO_MOCK=false');
  console.log('🔄 请重启开发服务器以使配置生效');
}

function showHelp() {
  console.log(`
🔄 环境切换脚本

📖 使用方法:
  node switch-env.js mock                           # 切换到Mock模式
  node switch-env.js real <API地址>                  # 切换到真实后台

📝 示例:
  node switch-env.js mock
  node switch-env.js real http://localhost:8080/api
  node switch-env.js real https://api.example.com/api

💡 提示:
  - 切换后需要重启开发服务器
  - Mock模式使用本地模拟数据
  - 真实模式连接实际后台接口
  `);
}

// 主逻辑
const args = process.argv.slice(2);
const mode = args[0];

if (!mode) {
  showHelp();
  process.exit(0);
}

switch (mode.toLowerCase()) {
  case 'mock':
    switchToMock();
    break;
  case 'real':
    switchToReal(args[1]);
    break;
  case 'help':
  case '-h':
  case '--help':
    showHelp();
    break;
  default:
    console.error(`❌ 未知模式: ${mode}`);
    showHelp();
    process.exit(1);
}
