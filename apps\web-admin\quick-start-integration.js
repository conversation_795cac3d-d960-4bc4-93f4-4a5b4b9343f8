#!/usr/bin/env node

/**
 * 快速启动接口对接脚本
 * 用于快速配置和测试后台接口对接
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function main() {
  console.log(`
🚀 Vue Vben Admin 后台接口对接快速启动工具

此工具将帮助您快速配置后台接口对接环境。
  `);

  try {
    // 1. 获取后台接口信息
    console.log('📋 第一步：配置后台接口信息');
    const apiUrl = await question('请输入后台接口地址 (例: https://api.example.com/v1): ');
    const testEndpoint = await question('请输入测试连通性的接口 (例: /health 或 /auth/profile): ');

    // 2. 选择对接阶段
    console.log('\n🎯 第二步：选择对接阶段');
    console.log('1. 第一阶段：仅认证接口');
    console.log('2. 第二阶段：认证 + 管理员管理');
    console.log('3. 第三阶段：认证 + 管理员 + 核心业务');
    console.log('4. 第四阶段：全部接口');
    
    const stage = await question('请选择对接阶段 (1-4): ');

    // 3. 确认是否备份当前配置
    const backup = await question('\n💾 是否备份当前Mock配置？(y/n): ');

    // 4. 执行配置
    console.log('\n⚙️ 正在配置环境...');

    // 备份配置
    if (backup.toLowerCase() === 'y') {
      backupCurrentConfig();
    }

    // 更新环境配置
    updateEnvironmentConfig(apiUrl);

    // 生成测试脚本
    generateTestScript(apiUrl, testEndpoint, stage);

    // 显示下一步操作
    showNextSteps(stage);

  } catch (error) {
    console.error('❌ 配置过程中出现错误:', error.message);
  } finally {
    rl.close();
  }
}

function backupCurrentConfig() {
  const envFile = path.join(__dirname, '.env.development');
  const backupFile = path.join(__dirname, `.env.development.backup.${Date.now()}`);
  
  if (fs.existsSync(envFile)) {
    fs.copyFileSync(envFile, backupFile);
    console.log(`✅ 配置已备份到: ${backupFile}`);
  }
}

function updateEnvironmentConfig(apiUrl) {
  const envFile = path.join(__dirname, '.env.development');
  
  let content = '';
  if (fs.existsSync(envFile)) {
    content = fs.readFileSync(envFile, 'utf8');
  }

  // 更新API地址
  content = content.replace(
    /VITE_GLOB_API_URL=.*/,
    `VITE_GLOB_API_URL=${apiUrl}`
  );

  // 关闭Mock服务
  content = content.replace(
    /VITE_NITRO_MOCK=.*/,
    'VITE_NITRO_MOCK=false'
  );

  fs.writeFileSync(envFile, content);
  console.log('✅ 环境配置已更新');
}

function generateTestScript(apiUrl, testEndpoint, stage) {
  const testScript = `
// 自动生成的接口测试脚本
// 生成时间: ${new Date().toLocaleString()}

import { ApiTester } from './src/utils/api-test';
import { EnvSwitcher } from './src/utils/env-switcher';

export async function runIntegrationTests() {
  console.log('🧪 开始接口对接测试...');
  
  // 1. 检查环境配置
  console.log('📋 当前环境配置:');
  EnvSwitcher.getEnvInfo();
  
  // 2. 测试连通性
  console.log('🔗 测试接口连通性...');
  const connectionResult = await EnvSwitcher.checkBackendConnection('${apiUrl}${testEndpoint}');
  if (!connectionResult.success) {
    console.error('❌ 接口连通性测试失败，请检查网络和接口地址');
    return;
  }
  
  // 3. 根据阶段执行测试
  ${generateStageTests(stage)}
  
  console.log('✅ 接口对接测试完成');
}

${generateStageTests(stage)}

// 在浏览器控制台中运行: runIntegrationTests()
if (typeof window !== 'undefined') {
  window.runIntegrationTests = runIntegrationTests;
}
`;

  fs.writeFileSync(path.join(__dirname, 'integration-test.js'), testScript);
  console.log('✅ 测试脚本已生成: integration-test.js');
}

function generateStageTests(stage) {
  const tests = {
    '1': `
  // 第一阶段：认证接口测试
  console.log('🔐 测试认证接口...');
  try {
    await ApiTester.testLogin();
    console.log('✅ 认证接口测试通过');
  } catch (error) {
    console.error('❌ 认证接口测试失败:', error);
  }`,
    
    '2': `
  // 第二阶段：认证 + 管理员管理测试
  console.log('🔐 测试认证接口...');
  await ApiTester.testLogin();
  
  console.log('👥 测试管理员管理接口...');
  // 添加管理员管理接口测试
  console.log('✅ 第二阶段测试完成');`,
    
    '3': `
  // 第三阶段：核心业务接口测试
  console.log('🔐 测试认证接口...');
  await ApiTester.testLogin();
  
  console.log('👥 测试管理员管理接口...');
  // 管理员管理测试
  
  console.log('🏢 测试核心业务接口...');
  // 产品、计划、财务、优惠券测试
  console.log('✅ 第三阶段测试完成');`,
    
    '4': `
  // 第四阶段：全部接口测试
  console.log('🔐 测试所有接口...');
  await ApiTester.testAllCoreApis();
  console.log('✅ 全部接口测试完成');`
  };

  return tests[stage] || tests['1'];
}

function showNextSteps(stage) {
  console.log(`
🎉 配置完成！接下来的步骤：

📋 第一步：重启开发服务器
   pnpm run dev

📋 第二步：在浏览器控制台运行测试
   runIntegrationTests()

📋 第三步：根据测试结果进行调整
   - 如果连通性测试失败，检查网络和接口地址
   - 如果认证测试失败，检查用户名密码和接口格式
   - 如果数据格式错误，修改 src/api/adapter.ts

📋 第四步：按阶段逐步对接
   当前选择的阶段：第${stage}阶段
   
📋 第五步：使用检查清单验证
   参考 INTEGRATION_CHECKLIST.md 进行全面检查

🔧 调试工具：
   - EnvSwitcher.getEnvInfo() - 查看环境信息
   - ApiTester.testAllCoreApis() - 测试所有接口
   - EnvSwitcher.checkBackendConnection() - 测试连通性

📚 参考文档：
   - BACKEND_INTEGRATION_PLAN.md - 详细对接计划
   - API_INTEGRATION_GUIDE.md - 对接指南
   - MANUAL_CONFIG_GUIDE.md - 手动配置指南

⚠️ 如果遇到问题：
   1. 检查 BACKEND_INTEGRATION_PLAN.md 中的风险应对方案
   2. 使用 node switch-env.js mock 回滚到Mock模式
   3. 查看浏览器控制台的详细错误信息

祝您对接顺利！🚀
  `);
}

// 运行主函数
main().catch(console.error);
