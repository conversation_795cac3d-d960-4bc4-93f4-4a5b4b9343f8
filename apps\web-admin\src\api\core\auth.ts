import { baseRequestClient, requestClient } from '#/api/request';

export namespace AuthApi {
  /** 登录接口参数 */
  export interface LoginParams {
    password: string;
    username: string;
  }

  /** 登录接口返回值 - 根据接口文档调整 */
  export interface LoginResult {
    token: string;
    admin: {
      id: number;
      username: string;
      nickname: string;
      status: string;
      last_login_time: string;
    };
    permissions: string[];
  }

  /** 管理员信息接口返回值 */
  export interface ProfileResult {
    id: number;
    username: string;
    nickname: string;
    status: string;
    last_login_time: string;
    roles: Array<{
      id: number;
      name: string;
    }>;
    permissions: string[];
  }

  /** 修改密码接口参数 */
  export interface ChangePasswordParams {
    old_password: string;
    new_password: string;
    confirm_password: string;
  }

  export interface RefreshTokenResult {
    data: string;
    status: number;
  }
}

/**
 * 登录
 */
export async function loginApi(data: AuthApi.LoginParams) {
  return requestClient.post<AuthApi.LoginResult>('/auth/login', data, {
    withCredentials: true,
  });
}

/**
 * 刷新accessToken
 */
export async function refreshTokenApi() {
  return baseRequestClient.post<AuthApi.RefreshTokenResult>(
    '/auth/refresh',
    null,
    {
      withCredentials: true,
    },
  );
}

/**
 * 退出登录
 */
export async function logoutApi() {
  return baseRequestClient.post('/auth/logout', null, {
    withCredentials: true,
  });
}

/**
 * 获取当前管理员信息
 */
export async function getProfileApi() {
  return requestClient.get<AuthApi.ProfileResult>('/auth/profile');
}

/**
 * 修改密码
 */
export async function changePasswordApi(data: AuthApi.ChangePasswordParams) {
  return requestClient.post('/auth/change-password', data);
}

/**
 * 获取用户权限码
 */
export async function getAccessCodesApi() {
  return requestClient.get<string[]>('/auth/codes');
}
