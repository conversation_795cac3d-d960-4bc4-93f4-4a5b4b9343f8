import { baseRequestClient, requestClient } from '#/api/request';

export namespace AuthApi {
  /** 登录接口参数 */
  export interface LoginParams {
    password: string;
    username: string;
  }

  /** 登录接口返回值 - 根据真实后端接口文档调整 */
  export interface LoginResult {
    token: string;
    admin: {
      id: number;
      username: string;
      nickname: string;
      status: string;
      last_login_time: string;
    };
    permissions: string[];
  }

  /** 兼容accessToken格式的登录返回值 */
  export interface LoginApiResult {
    accessToken: string;
    userInfo?: any;
    permissions?: string[];
  }

  /** 管理员信息接口返回值 */
  export interface ProfileResult {
    id: number;
    username: string;
    nickname: string;
    status: string;
    last_login_time: string;
    roles: Array<{
      id: number;
      name: string;
    }>;
    permissions: string[];
  }

  /** 修改密码接口参数 */
  export interface ChangePasswordParams {
    old_password: string;
    new_password: string;
    confirm_password: string;
  }

  export interface RefreshTokenResult {
    data: string;
    status: number;
  }
}

/**
 * 登录 - 适配真实后端API
 */
export async function loginApi(data: AuthApi.LoginParams) {
  try {
    // 移除 withCredentials 以避免CORS问题
    const response = await requestClient.post<AuthApi.LoginResult>('/auth/login', data);

    // 将真实后端返回格式转换为前端期望的格式
    return {
      accessToken: response.token,
      userInfo: response.admin,
      permissions: response.permissions,
    };
  } catch (error) {
    console.error('登录API调用失败:', error);
    throw error;
  }
}

/**
 * 刷新accessToken
 */
export async function refreshTokenApi() {
  return baseRequestClient.post<AuthApi.RefreshTokenResult>(
    '/auth/refresh',
    null
  );
}

/**
 * 退出登录
 */
export async function logoutApi() {
  return baseRequestClient.post('/auth/logout', null);
}

/**
 * 获取当前管理员信息
 */
export async function getProfileApi() {
  return requestClient.get<AuthApi.ProfileResult>('/auth/profile');
}

/**
 * 修改密码
 */
export async function changePasswordApi(data: AuthApi.ChangePasswordParams) {
  return requestClient.post('/auth/change-password', data);
}

/**
 * 获取用户权限码 - 适配真实后端API
 */
export async function getAccessCodesApi() {
  try {
    return await requestClient.get<string[]>('/auth/codes');
  } catch (error) {
    console.warn('获取权限码失败，返回空数组:', error);
    // 如果权限码接口失败，返回空数组，避免阻塞登录流程
    return [];
  }
}
