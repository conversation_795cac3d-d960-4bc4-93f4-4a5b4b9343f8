import { requestClient } from '#/api/request';
import { transformPageParams, transformPageResponse } from '#/api/adapter';
import type { PageFetchParams } from '#/api/request';

export namespace ProductApi {
  /** 应用信息接口 - 动态键值对 */
  export interface AppInfo {
    [key: string]: string;
  }

  /** 应用信息项接口 - 用于表单编辑 */
  export interface AppInfoItem {
    key: string;
    value: string;
    id?: string; // 用于表单组件的唯一标识
  }

  /** 产品图片接口 */
  export interface ProductImage {
    id: number;
    image_url: string;
    image_type: string;
    sort_order: number;
  }

  /** 产品详情接口 - 根据真实JSON数据结构定义 */
  export interface Product {
    [key: string]: any;
    id: string;
    productCode: string;
    productName: string;
    productCategory: string;
    companyName: string;
    productDescription?: string;
    status: 0 | 1;
    productLogo?: string;
    productMainImage?: string;
    createTime?: string;
    // 新增字段以匹配JSON结构
    google_play_link?: string;
    app_store_link?: string;
    app_info?: AppInfo;
    images?: ProductImage[];
  }

  /** 后端API数据结构 - 与JSON完全匹配 */
  export interface BackendProduct {
    id: number;
    name: string;
    category: string;
    company: string;
    description: string;
    logo: string;
    image: string;
    google_play_link: string;
    app_store_link: string;
    status: string;
    app_info: AppInfo;
    images?: ProductImage[];
  }

  /** 创建/更新产品参数 - 后端格式 */
  export interface ProductSubmitData {
    name: string;
    category: string;
    company: string;
    description: string;
    logo: string;
    image: string;
    google_play_link?: string;
    app_store_link?: string;
    status?: string;
    app_info?: AppInfo;
  }

  /** 产品列表查询参数 */
  export interface ProductListParams extends PageFetchParams {
    name?: string;
    category?: string;
    company?: string;
    status?: string;
  }
}

/**
 * 将后端数据转换为前端格式
 */
function transformBackendToFrontend(backendData: ProductApi.BackendProduct): ProductApi.Product {
  return {
    id: backendData.id?.toString() || '',
    productCode: `PROD-${backendData.id}`, // 生成产品编码
    productName: backendData.name || '',
    productCategory: backendData.category || '',
    companyName: backendData.company || '',
    productDescription: backendData.description || '',
    status: backendData.status === '生效' ? 1 : 0,
    productLogo: backendData.logo || '',
    productMainImage: backendData.image || '',
    createTime: new Date().toISOString(),
    google_play_link: backendData.google_play_link || '',
    app_store_link: backendData.app_store_link || '',
    app_info: backendData.app_info,
    images: backendData.images || [],
  };
}

/**
 * 将前端数据转换为后端格式
 */
function transformFrontendToBackend(frontendData: ProductApi.Product): ProductApi.ProductSubmitData {
  return {
    name: frontendData.productName || '',
    category: frontendData.productCategory || '',
    company: frontendData.companyName || '',
    description: frontendData.productDescription || '',
    logo: frontendData.productLogo || '',
    image: frontendData.productMainImage || '',
    google_play_link: frontendData.google_play_link || '',
    app_store_link: frontendData.app_store_link || '',
    status: frontendData.status === 1 ? '生效' : '失效',
    app_info: frontendData.app_info || {},
  };
}

/**
 * 将app_info数组转换为对象格式
 */
export function transformAppInfoArrayToObject(appInfoArray: ProductApi.AppInfoItem[]): ProductApi.AppInfo {
  const result: ProductApi.AppInfo = {};
  appInfoArray.forEach(item => {
    if (item.key && item.key.trim()) {
      result[item.key] = item.value || '';
    }
  });
  return result;
}

/**
 * 将app_info对象转换为数组格式
 */
export function transformAppInfoObjectToArray(appInfoObject: ProductApi.AppInfo): ProductApi.AppInfoItem[] {
  return Object.entries(appInfoObject || {}).map(([key, value], index) => ({
    id: `app_info_${index}`,
    key,
    value: value || '',
  }));
}

/**
 * 获取产品列表数据 - 适配真实API和前端字段格式
 */
export async function getProductList(params: ProductApi.ProductListParams = {}) {
  try {
    console.log('🔄 调用产品列表API，参数:', params);

    // 转换分页参数
    const transformedParams = transformPageParams(params);
    console.log('📤 转换后的参数:', transformedParams);

    // 调用API
    const response = await requestClient.get('/products', {
      params: transformedParams,
    });

    console.log('📥 API原始响应:', response);

    // 转换响应格式
    const transformedResponse = transformPageResponse(response);
    console.log('🔄 转换后的响应:', transformedResponse);

    // 使用转换函数处理数据
    if (transformedResponse.items && Array.isArray(transformedResponse.items)) {
      transformedResponse.items = transformedResponse.items.map((item: any) =>
        transformBackendToFrontend(item as ProductApi.BackendProduct)
      );
    }

    console.log('✅ 最终返回数据:', transformedResponse);
    return transformedResponse;
  } catch (error) {
    console.error('❌ 获取产品列表失败:', error);
    throw error;
  }
}

/**
 * 创建产品
 * @param data 产品数据（前端格式）
 */
export async function createProduct(
  data: Omit<ProductApi.Product, 'createTime' | 'id'>,
) {
  try {
    console.log('🔄 创建产品，前端数据:', data);

    // 转换为后端格式
    const backendData = transformFrontendToBackend(data as ProductApi.Product);
    console.log('📤 转换后的后端数据:', backendData);

    const result = await requestClient.post('/products', backendData);
    console.log('✅ 创建产品成功:', result);

    return result;
  } catch (error) {
    console.error('❌ 创建产品失败:', error);
    throw error;
  }
}

/**
 * 更新产品
 * @param id 产品 ID
 * @param data 产品数据（前端格式）
 */
export async function updateProduct(
  id: string,
  data: Omit<ProductApi.Product, 'createTime' | 'id'>,
) {
  try {
    console.log('🔄 更新产品，前端数据:', data);

    // 转换为后端格式
    const backendData = transformFrontendToBackend(data as ProductApi.Product);
    console.log('📤 转换后的后端数据:', backendData);

    const result = await requestClient.put(`/products/${id}`, backendData);
    console.log('✅ 更新产品成功:', result);

    return result;
  } catch (error) {
    console.error('❌ 更新产品失败:', error);
    throw error;
  }
}

/**
 * 删除产品
 * @param id 产品 ID
 */
export async function deleteProduct(id: string) {
  try {
    return await requestClient.delete(`/products/${id}`);
  } catch (error) {
    console.error('删除产品失败:', error);
    throw error;
  }
}
