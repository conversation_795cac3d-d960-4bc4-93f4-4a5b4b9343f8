import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

export namespace ProductApi {
  export interface Product {
    [key: string]: any;
    id: string;
    productCode: string;
    productName: string;
    productCategory: string;
    companyName: string;
    productDescription?: string;
    status: 0 | 1;
    productLogo?: string;
    productMainImage?: string;
    createTime?: string;
  }
}

/**
 * 获取产品列表数据
 */
async function getProductList(params: Recordable<any>) {
  return requestClient.get<Array<ProductApi.Product>>('/product/list', {
    params,
  });
}

/**
 * 创建产品
 * @param data 产品数据
 */
async function createProduct(
  data: Omit<ProductApi.Product, 'createTime' | 'id'>,
) {
  return requestClient.post('/product', data);
}

/**
 * 更新产品
 *
 * @param id 产品 ID
 * @param data 产品数据
 */
async function updateProduct(
  id: string,
  data: Omit<ProductApi.Product, 'createTime' | 'id'>,
) {
  return requestClient.put(`/product/${id}`, data);
}

/**
 * 删除产品
 * @param id 产品 ID
 */
async function deleteProduct(id: string) {
  return requestClient.delete(`/product/${id}`);
}

export { createProduct, deleteProduct, getProductList, updateProduct };
