import { requestClient } from '#/api/request';
import { transformPageParams, transformPageResponse } from '#/api/adapter';
import type { PageFetchParams } from '#/api/request';

export namespace ProductApi {
  /** 应用信息接口 */
  export interface AppInfo {
    category?: string;
    languages?: string;
    age_rating?: string;
    ios_requires?: string;
    android_requires?: string;
  }

  /** 产品图片接口 */
  export interface ProductImage {
    id: number;
    image_url: string;
    image_type: string;
    sort_order: number;
  }

  /** 产品详情接口 - 根据接口文档定义 */
  export interface Product {
    [key: string]: any;
    id: string;
    productCode: string;
    productName: string;
    productCategory: string;
    companyName: string;
    productDescription?: string;
    status: 0 | 1;
    productLogo?: string;
    productMainImage?: string;
    createTime?: string;
  }

  /** 产品列表查询参数 */
  export interface ProductListParams extends PageFetchParams {
    name?: string;
    category?: string;
    company?: string;
    status?: string;
  }
}

/**
 * 获取产品列表数据 - 适配真实API和前端字段格式
 */
export async function getProductList(params: ProductApi.ProductListParams = {}) {
  try {
    console.log('🔄 调用产品列表API，参数:', params);

    // 转换分页参数
    const transformedParams = transformPageParams(params);
    console.log('📤 转换后的参数:', transformedParams);

    // 调用API
    const response = await requestClient.get('/products', {
      params: transformedParams,
    });

    console.log('📥 API原始响应:', response);

    // 转换响应格式
    const transformedResponse = transformPageResponse(response);
    console.log('🔄 转换后的响应:', transformedResponse);

    // 转换字段名以匹配前端期望
    if (transformedResponse.items && Array.isArray(transformedResponse.items)) {
      transformedResponse.items = transformedResponse.items.map((item: any) => ({
        id: item.id?.toString() || '',
        productCode: item.product_code || item.productCode || '',
        productName: item.name || item.productName || '',
        productCategory: item.category || item.productCategory || '',
        companyName: item.company || item.companyName || '',
        productDescription: item.description || item.productDescription || '',
        status: item.status === '生效' ? 1 : 0,
        productLogo: item.logo || item.productLogo || '',
        productMainImage: item.image || item.productMainImage || '',
        createTime: item.create_time || item.createTime || '',
      }));
    }

    console.log('✅ 最终返回数据:', transformedResponse);
    return transformedResponse;
  } catch (error) {
    console.error('❌ 获取产品列表失败:', error);
    throw error;
  }
}

/**
 * 创建产品
 * @param data 产品数据
 */
export async function createProduct(
  data: Omit<ProductApi.Product, 'createTime' | 'id'>,
) {
  try {
    return await requestClient.post('/products', data);
  } catch (error) {
    console.error('创建产品失败:', error);
    throw error;
  }
}

/**
 * 更新产品
 * @param id 产品 ID
 * @param data 产品数据
 */
export async function updateProduct(
  id: string,
  data: Omit<ProductApi.Product, 'createTime' | 'id'>,
) {
  try {
    return await requestClient.put(`/products/${id}`, data);
  } catch (error) {
    console.error('更新产品失败:', error);
    throw error;
  }
}

/**
 * 删除产品
 * @param id 产品 ID
 */
export async function deleteProduct(id: string) {
  try {
    return await requestClient.delete(`/products/${id}`);
  } catch (error) {
    console.error('删除产品失败:', error);
    throw error;
  }
}
