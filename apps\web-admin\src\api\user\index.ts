import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';
import { BaseApiService } from '#/api/base';
import type { PageFetchParams } from '#/api/request';

export namespace UserApi {
  export interface User {
    [key: string]: any;
    userId: string;
    username: string;
    nickname: string;
    avatar?: string;
    adminId: string;
    vipLevel: number;
    parentUserId?: string;
    agencyRelation: string;
    languagePreference: string;
    facebookId?: string;
    authCode: string;
    withdrawMethod: string;
    availableBalance: number;
    status: 0 | 1;
    userPerspective: string;
    createTime?: string;
    updateTime?: string;
  }
}

// 创建用户API服务实例
const userApiService = new BaseApiService('/user');

/**
 * 获取用户列表数据
 */
async function getUserList(params: PageFetchParams) {
  return userApiService.getList<UserApi.User>(params);
}

/**
 * 更新用户
 *
 * @param id 用户 ID
 * @param data 用户数据
 */
async function updateUser(
  id: string,
  data: Omit<UserApi.User, 'createTime' | 'updateTime' | 'userId'>,
) {
  return userApiService.update(id, data);
}

/**
 * 获取用户详情
 * @param id 用户 ID
 */
async function getUserDetail(id: string) {
  return userApiService.getDetail<UserApi.User>(id);
}

/**
 * 创建用户
 */
async function createUser(data: Omit<UserApi.User, 'createTime' | 'updateTime' | 'userId'>) {
  return userApiService.create(data);
}

/**
 * 删除用户
 */
async function deleteUser(id: string) {
  return userApiService.delete(id);
}

/**
 * 批量删除用户
 */
async function batchDeleteUsers(ids: string[]) {
  return userApiService.batchDelete(ids);
}

/**
 * 更新用户状态
 */
async function updateUserStatus(id: string, status: 0 | 1) {
  return userApiService.updateStatus(id, status);
}

export {
  getUserDetail,
  getUserList,
  updateUser,
  createUser,
  deleteUser,
  batchDeleteUsers,
  updateUserStatus
};
