/**
 * 产品API调试工具
 * 用于调试产品列表数据显示问题
 */

import { getProductList } from '#/api/product';

export interface DebugResult {
  success: boolean;
  message: string;
  data?: any;
  error?: any;
}

/**
 * 测试产品列表API调用
 */
export async function testProductListApi(): Promise<DebugResult> {
  try {
    console.log('🔍 开始测试产品列表API...');
    
    const params = {
      page: 1,
      pageSize: 10,
    };
    
    console.log('📤 请求参数:', params);
    
    const result = await getProductList(params);
    
    console.log('📥 API返回结果:', result);
    
    return {
      success: true,
      message: '产品列表API调用成功',
      data: {
        total: result.total,
        itemsCount: result.items?.length || 0,
        items: result.items?.slice(0, 3) || [], // 只显示前3条数据
        structure: result.items?.[0] ? Object.keys(result.items[0]) : [],
      },
    };
  } catch (error: any) {
    console.error('❌ 产品列表API调用失败:', error);
    
    return {
      success: false,
      message: `产品列表API调用失败: ${error.message || '未知错误'}`,
      error: {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message,
      },
    };
  }
}

/**
 * 检查数据字段映射
 */
export function checkFieldMapping(apiData: any): {
  hasCorrectFields: boolean;
  missingFields: string[];
  extraFields: string[];
  fieldMapping: Record<string, any>;
} {
  const expectedFields = [
    'id',
    'productCode',
    'productName', 
    'productCategory',
    'companyName',
    'productDescription',
    'status',
    'productLogo',
    'productMainImage',
    'createTime',
  ];
  
  if (!apiData || typeof apiData !== 'object') {
    return {
      hasCorrectFields: false,
      missingFields: expectedFields,
      extraFields: [],
      fieldMapping: {},
    };
  }
  
  const actualFields = Object.keys(apiData);
  const missingFields = expectedFields.filter(field => !actualFields.includes(field));
  const extraFields = actualFields.filter(field => !expectedFields.includes(field));
  
  const fieldMapping: Record<string, any> = {};
  expectedFields.forEach(field => {
    fieldMapping[field] = apiData[field];
  });
  
  return {
    hasCorrectFields: missingFields.length === 0,
    missingFields,
    extraFields,
    fieldMapping,
  };
}

/**
 * 模拟VxeTable数据处理
 */
export function simulateVxeTableProcessing(apiResponse: any): {
  processedData: any;
  issues: string[];
} {
  const issues: string[] = [];
  
  console.log('🔄 模拟VxeTable数据处理...');
  console.log('原始响应:', apiResponse);
  
  // 检查响应结构
  if (!apiResponse) {
    issues.push('API响应为空');
    return { processedData: null, issues };
  }
  
  if (!apiResponse.items) {
    issues.push('响应中缺少items字段');
    return { processedData: apiResponse, issues };
  }
  
  if (!Array.isArray(apiResponse.items)) {
    issues.push('items字段不是数组');
    return { processedData: apiResponse, issues };
  }
  
  if (apiResponse.items.length === 0) {
    issues.push('items数组为空');
    return { processedData: apiResponse, issues };
  }
  
  // 检查第一条数据的字段
  const firstItem = apiResponse.items[0];
  const fieldCheck = checkFieldMapping(firstItem);
  
  if (!fieldCheck.hasCorrectFields) {
    issues.push(`缺少字段: ${fieldCheck.missingFields.join(', ')}`);
  }
  
  if (fieldCheck.extraFields.length > 0) {
    console.log('额外字段:', fieldCheck.extraFields);
  }
  
  return {
    processedData: apiResponse,
    issues,
  };
}

/**
 * 完整的产品列表调试
 */
export async function debugProductList(): Promise<{
  apiTest: DebugResult;
  fieldCheck?: any;
  vxeSimulation?: any;
  recommendations: string[];
}> {
  console.log('🚀 开始完整的产品列表调试...');
  
  const apiTest = await testProductListApi();
  const recommendations: string[] = [];
  
  if (!apiTest.success) {
    recommendations.push('修复API连接问题');
    return { apiTest, recommendations };
  }
  
  let fieldCheck, vxeSimulation;
  
  if (apiTest.data?.items?.[0]) {
    fieldCheck = checkFieldMapping(apiTest.data.items[0]);
    
    if (!fieldCheck.hasCorrectFields) {
      recommendations.push('修复字段映射问题');
      recommendations.push(`缺少字段: ${fieldCheck.missingFields.join(', ')}`);
    }
  }
  
  vxeSimulation = simulateVxeTableProcessing(apiTest.data);
  
  if (vxeSimulation.issues.length > 0) {
    recommendations.push('修复VxeTable数据处理问题');
    recommendations.push(...vxeSimulation.issues);
  }
  
  if (recommendations.length === 0) {
    recommendations.push('数据结构正常，检查VxeTable配置');
  }
  
  console.log('📊 调试结果汇总:');
  console.log('API测试:', apiTest.success ? '✅ 成功' : '❌ 失败');
  console.log('字段检查:', fieldCheck?.hasCorrectFields ? '✅ 正常' : '❌ 异常');
  console.log('VxeTable模拟:', vxeSimulation?.issues.length === 0 ? '✅ 正常' : '❌ 异常');
  console.log('建议:', recommendations);
  
  return {
    apiTest,
    fieldCheck,
    vxeSimulation,
    recommendations,
  };
}

/**
 * 检查网络请求详情
 */
export async function checkNetworkRequest(): Promise<DebugResult> {
  try {
    console.log('🌐 检查网络请求详情...');
    
    // 直接调用底层API，不经过数据转换
    const response = await fetch('/api/products?page=1&page_size=10', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
        'Content-Type': 'application/json',
      },
    });
    
    const data = await response.json();
    
    console.log('🔍 原始网络响应:', {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      data,
    });
    
    return {
      success: response.ok,
      message: `网络请求${response.ok ? '成功' : '失败'}`,
      data: {
        status: response.status,
        statusText: response.statusText,
        responseData: data,
        dataType: typeof data,
        isArray: Array.isArray(data),
        hasItems: data && typeof data === 'object' && 'items' in data,
        hasTotal: data && typeof data === 'object' && 'total' in data,
      },
    };
  } catch (error: any) {
    console.error('❌ 网络请求失败:', error);
    
    return {
      success: false,
      message: `网络请求失败: ${error.message}`,
      error,
    };
  }
}

// 在开发环境下，将调试函数挂载到全局对象上
if (import.meta.env.DEV) {
  (window as any).productDebug = {
    testProductListApi,
    checkFieldMapping,
    simulateVxeTableProcessing,
    debugProductList,
    checkNetworkRequest,
  };
  
  console.log('🔧 产品调试工具已加载，可在控制台使用:');
  console.log('- window.productDebug.testProductListApi()');
  console.log('- window.productDebug.debugProductList()');
  console.log('- window.productDebug.checkNetworkRequest()');
}
