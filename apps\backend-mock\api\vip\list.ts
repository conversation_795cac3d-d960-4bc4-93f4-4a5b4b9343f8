import { faker } from '@faker-js/faker';
import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, usePageResponseSuccess } from '~/utils/response';

const formatterCN = new Intl.DateTimeFormat('zh-CN', {
  timeZone: 'Asia/Shanghai',
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
});

const vipLevelNames = [
  '青铜会员',
  '白银会员',
  '黄金会员',
  '铂金会员',
  '钻石会员',
  '至尊会员',
  '皇冠会员',
  '传奇会员',
];

const vipPrivileges = [
  '专属客服、优先处理、免费咨询',
  '专属客服、优先处理、免费咨询、专属折扣',
  '专属客服、优先处理、免费咨询、专属折扣、生日礼品',
  '专属客服、优先处理、免费咨询、专属折扣、生日礼品、积分翻倍',
  '专属客服、优先处理、免费咨询、专属折扣、生日礼品、积分翻倍、免费升级',
  '专属客服、优先处理、免费咨询、专属折扣、生日礼品、积分翻倍、免费升级、专属活动',
  '专属客服、优先处理、免费咨询、专属折扣、生日礼品、积分翻倍、免费升级、专属活动、无限制访问',
  '专属客服、优先处理、免费咨询、专属折扣、生日礼品、积分翻倍、免费升级、专属活动、无限制访问、专属顾问',
];

function generateMockDataList(count: number) {
  const dataList = [];

  for (let i = 0; i < count; i++) {
    const levelValue = i + 1;
    const dataItem: Record<string, any> = {
      id: faker.string.uuid(),
      icon: faker.image.avatar(),
      levelName: vipLevelNames[i % vipLevelNames.length],
      levelValue,
      clickPrice: Number(faker.finance.amount({ min: 0.1, max: 10, dec: 2 })),
      commissionDetail: `等级${levelValue}佣金：${faker.finance.amount({ min: 5, max: 50, dec: 2 })}%`,
      purchasePrice: Number(
        faker.finance.amount({ min: 100, max: 10_000, dec: 2 }),
      ),
      dataLimit: faker.number.int({ min: 100, max: 10_000 }),
      vipPrivileges: vipPrivileges[i % vipPrivileges.length],
      createTime: formatterCN.format(
        faker.date.between({ from: '2022-01-01', to: '2025-01-01' }),
      ),
      updateTime: formatterCN.format(
        faker.date.between({ from: '2023-01-01', to: '2025-01-01' }),
      ),
    };

    dataList.push(dataItem);
  }

  return dataList;
}

const mockData = generateMockDataList(50);

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const {
    page = 1,
    pageSize = 20,
    levelName,
    levelValue,
    startTime,
    endTime,
  } = getQuery(event);

  let listData = structuredClone(mockData);

  if (levelName) {
    listData = listData.filter((item) =>
      item.levelName.toLowerCase().includes(String(levelName).toLowerCase()),
    );
  }

  if (levelValue) {
    listData = listData.filter(
      (item) => item.levelValue === Number(levelValue),
    );
  }

  if (startTime) {
    listData = listData.filter((item) => item.createTime >= startTime);
  }

  if (endTime) {
    listData = listData.filter((item) => item.createTime <= endTime);
  }

  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
