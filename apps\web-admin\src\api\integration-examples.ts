/**
 * 接口对接示例代码
 * 展示如何使用BaseApiService和适配器进行接口对接
 */

import { BaseApiService } from './base';
import { transformPageParams, transformPageResponse } from './adapter';
import { requestClient } from './request';

// ==================== 第一阶段：认证接口示例 ====================

/**
 * 认证服务示例
 */
export class AuthService {
  /**
   * 登录接口对接示例
   */
  static async login(username: string, password: string) {
    try {
      const response = await requestClient.post('/auth/login', {
        username,
        password,
      });
      
      // 根据接口文档，返回格式为 {token, admin, permissions}
      return {
        accessToken: response.token,
        userInfo: response.admin,
        permissions: response.permissions,
      };
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户信息示例
   */
  static async getProfile() {
    return requestClient.get('/auth/profile');
  }
}

// ==================== 第二阶段：管理员管理示例 ====================

/**
 * 管理员管理服务示例
 */
export class AdminService extends BaseApiService {
  constructor() {
    super('/admin');
  }

  /**
   * 获取角色列表
   */
  async getRoleList(params: any = {}) {
    const transformedParams = transformPageParams(params);
    const response = await requestClient.get('/admin/roles', {
      params: transformedParams,
    });
    return transformPageResponse(response);
  }

  /**
   * 创建角色
   */
  async createRole(data: {
    name: string;
    description?: string;
    parent_id?: number;
    permissions: number[];
    status?: string;
    sort_order?: number;
  }) {
    return requestClient.post('/admin/roles', data);
  }

  /**
   * 获取权限树
   */
  async getPermissionTree(type?: string) {
    return requestClient.get('/admin/permissions/tree', {
      params: { type },
    });
  }

  /**
   * 获取管理员列表
   */
  async getAdminList(params: any = {}) {
    const transformedParams = transformPageParams(params);
    const response = await requestClient.get('/admin/list', {
      params: transformedParams,
    });
    return transformPageResponse(response);
  }
}

// ==================== 第三阶段：业务模块示例 ====================

/**
 * 产品管理服务示例
 */
export class ProductService extends BaseApiService {
  constructor() {
    super('/products');
  }

  /**
   * 获取产品列表（重写以适配接口文档格式）
   */
  async getList(params: any = {}) {
    const transformedParams = transformPageParams(params);
    const response = await requestClient.get('/products', {
      params: transformedParams,
    });
    return transformPageResponse(response);
  }

  /**
   * 添加产品图片
   */
  async addProductImage(productId: number, imageData: {
    image_url: string;
    image_type: string;
    sort_order?: number;
  }) {
    return requestClient.post(`/products/${productId}/images`, imageData);
  }

  /**
   * 删除产品图片
   */
  async deleteProductImage(imageId: number) {
    return requestClient.delete(`/products/images/${imageId}`);
  }
}

/**
 * 计划管理服务示例
 */
export class PlanService extends BaseApiService {
  constructor() {
    super('/plans');
  }

  /**
   * 修改计划状态
   */
  async updateStatus(id: number, status: string) {
    return requestClient.put(`/plans/${id}/status`, { status });
  }
}

/**
 * 财务管理服务示例
 */
export class FinanceService {
  /**
   * 获取充值记录
   */
  static async getRechargeList(params: any = {}) {
    const transformedParams = transformPageParams(params);
    const response = await requestClient.get('/finance/recharge', {
      params: transformedParams,
    });
    return transformPageResponse(response);
  }

  /**
   * 为用户充值
   */
  static async rechargeUser(data: {
    user_id: number;
    amount: number;
    type: string;
    remark?: string;
  }) {
    return requestClient.post('/finance/recharge', data);
  }

  /**
   * 批量充值
   */
  static async batchRecharge(data: {
    user_ids: number[];
    amount: number;
    type: string;
    remark?: string;
  }) {
    return requestClient.post('/finance/recharge/batch', data);
  }

  /**
   * 获取提现记录
   */
  static async getWithdrawList(params: any = {}) {
    const transformedParams = transformPageParams(params);
    const response = await requestClient.get('/finance/withdraw', {
      params: transformedParams,
    });
    return transformPageResponse(response);
  }

  /**
   * 审核提现申请
   */
  static async auditWithdraw(id: number, status: string, remark?: string) {
    return requestClient.put(`/finance/withdraw/${id}/audit`, {
      status,
      remark,
    });
  }

  /**
   * 导出交易记录
   */
  static async exportTransactions(params: any = {}) {
    return requestClient.get('/finance/transactions/export', {
      params,
      responseType: 'blob',
    });
  }
}

/**
 * 优惠券管理服务示例
 */
export class CouponService extends BaseApiService {
  constructor() {
    super('/coupons');
  }

  /**
   * 修改优惠券状态
   */
  async updateStatus(id: number, status: string) {
    return requestClient.put(`/coupons/${id}/status`, { status });
  }

  /**
   * 获取用户优惠券列表
   */
  async getUserCouponList(params: any = {}) {
    const transformedParams = transformPageParams(params);
    const response = await requestClient.get('/user-coupons', {
      params: transformedParams,
    });
    return transformPageResponse(response);
  }

  /**
   * 批量发放优惠券
   */
  async batchDistribute(data: {
    user_ids: number[];
    coupon_id: number;
    remark?: string;
  }) {
    return requestClient.post('/user-coupons/batch', data);
  }
}

// ==================== 使用示例 ====================

/**
 * 使用示例函数
 */
export async function integrationExamples() {
  try {
    // 1. 认证示例
    const loginResult = await AuthService.login('admin', 'password');
    console.log('登录成功:', loginResult);

    // 2. 管理员管理示例
    const adminService = new AdminService();
    const roleList = await adminService.getRoleList({ page: 1, page_size: 10 });
    console.log('角色列表:', roleList);

    // 3. 产品管理示例
    const productService = new ProductService();
    const productList = await productService.getList({ page: 1, page_size: 20 });
    console.log('产品列表:', productList);

    // 4. 财务管理示例
    const rechargeList = await FinanceService.getRechargeList({ page: 1 });
    console.log('充值记录:', rechargeList);

    // 5. 优惠券管理示例
    const couponService = new CouponService();
    const couponList = await couponService.getList({ page: 1 });
    console.log('优惠券列表:', couponList);

  } catch (error) {
    console.error('接口调用失败:', error);
  }
}

// 在开发环境下暴露示例函数
if (import.meta.env.DEV) {
  (window as any).integrationExamples = integrationExamples;
  console.log('🔧 接口对接示例已加载，可在控制台使用 integrationExamples() 进行测试');
}
