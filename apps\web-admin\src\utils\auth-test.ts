/**
 * 登录API测试工具
 * 用于测试真实后端API的连接和响应
 */

import { loginApi } from '#/api/core/auth';
import { getUserInfoApi } from '#/api/core/user';

export interface LoginTestResult {
  success: boolean;
  message: string;
  data?: any;
  error?: any;
}

/**
 * 测试登录API连接
 */
export async function testLoginConnection(): Promise<LoginTestResult> {
  try {
    // 使用测试账号进行登录测试
    const testCredentials = {
      username: 'test',
      password: 'test123',
    };

    console.log('🔄 正在测试登录API连接...');
    console.log('API地址:', import.meta.env.VITE_GLOB_API_URL);
    console.log('Mock模式:', import.meta.env.VITE_NITRO_MOCK);
    console.log('实际代理到:', import.meta.env.VITE_NITRO_MOCK === 'false' ? 'https://www.fb-hc-ads.click/mng/' : 'Mock服务');
    console.log('测试账号:', testCredentials.username);

    const result = await login<PERSON>pi(testCredentials);
    
    return {
      success: true,
      message: '登录API连接成功',
      data: {
        hasAccessToken: !!result.accessToken,
        hasUserInfo: !!result.userInfo,
        hasPermissions: !!result.permissions,
        tokenLength: result.accessToken?.length || 0,
      },
    };
  } catch (error: any) {
    console.error('❌ 登录API测试失败:', error);
    
    return {
      success: false,
      message: `登录API连接失败: ${error.message || '未知错误'}`,
      error: {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message,
      },
    };
  }
}

/**
 * 测试用户信息API
 */
export async function testUserInfoApi(token?: string): Promise<LoginTestResult> {
  try {
    console.log('🔄 正在测试用户信息API...');
    
    const userInfo = await getUserInfoApi();
    
    return {
      success: true,
      message: '用户信息API连接成功',
      data: {
        userId: userInfo.userId,
        username: userInfo.username,
        realName: userInfo.realName,
        hasRoles: !!userInfo.roles?.length,
      },
    };
  } catch (error: any) {
    console.error('❌ 用户信息API测试失败:', error);
    
    return {
      success: false,
      message: `用户信息API连接失败: ${error.message || '未知错误'}`,
      error: {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message,
      },
    };
  }
}

/**
 * 测试权限码API - 已移除
 * 权限控制现在完全由后端token处理，不再需要前端获取权限码
 */
export async function testAccessCodesApi(): Promise<LoginTestResult> {
  console.log('ℹ️ 权限码API已移除，权限控制由后端token处理');

  return {
    success: true,
    message: '权限码API已移除 - 权限控制由后端token处理',
    data: {
      note: '不再需要前端获取权限码，所有权限验证由后端根据token进行',
      tokenBased: true,
    },
  };
}

/**
 * 运行完整的登录流程测试 - 简化版本
 */
export async function runFullLoginTest(): Promise<{
  loginTest: LoginTestResult;
  userInfoTest: LoginTestResult;
  tokenTest: LoginTestResult;
  summary: {
    totalTests: number;
    passedTests: number;
    failedTests: number;
    success: boolean;
  };
}> {
  console.log('🚀 开始运行简化的登录流程测试...');
  console.log('ℹ️ 权限控制已简化为基于token的验证');

  const loginTest = await testLoginConnection();
  const userInfoTest = await testUserInfoApi();
  const tokenTest = await testAccessCodesApi(); // 重用函数名，但实际测试token机制

  const tests = [loginTest, userInfoTest, tokenTest];
  const passedTests = tests.filter(test => test.success).length;
  const failedTests = tests.length - passedTests;

  const summary = {
    totalTests: tests.length,
    passedTests,
    failedTests,
    success: passedTests === tests.length,
  };

  console.log('📊 测试结果汇总:');
  console.log(`总测试数: ${summary.totalTests}`);
  console.log(`通过: ${summary.passedTests}`);
  console.log(`失败: ${summary.failedTests}`);
  console.log(`整体状态: ${summary.success ? '✅ 成功' : '❌ 失败'}`);
  console.log('');
  console.log('🔐 权限验证说明:');
  console.log('- 所有API请求自动携带Bearer token');
  console.log('- 后端根据token判断用户身份和权限');
  console.log('- 前端不再维护权限码列表');

  return {
    loginTest,
    userInfoTest,
    tokenTest,
    summary,
  };
}

// 在开发环境下，将测试函数挂载到全局对象上，方便在控制台调用
if (import.meta.env.DEV) {
  (window as any).authTest = {
    testLoginConnection,
    testUserInfoApi,
    testAccessCodesApi,
    runFullLoginTest,
  };
  
  console.log('🔧 登录API测试工具已加载，可在控制台使用:');
  console.log('- window.authTest.testLoginConnection()');
  console.log('- window.authTest.testUserInfoApi()');
  console.log('- window.authTest.testAccessCodesApi()');
  console.log('- window.authTest.runFullLoginTest()');
}
