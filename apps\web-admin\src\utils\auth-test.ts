/**
 * 登录API测试工具
 * 用于测试真实后端API的连接和响应
 */

import { loginApi, getAccessCodesApi } from '#/api/core/auth';
import { getUserInfoApi } from '#/api/core/user';

export interface LoginTestResult {
  success: boolean;
  message: string;
  data?: any;
  error?: any;
}

/**
 * 测试登录API连接
 */
export async function testLoginConnection(): Promise<LoginTestResult> {
  try {
    // 使用测试账号进行登录测试
    const testCredentials = {
      username: 'test',
      password: 'test123',
    };

    console.log('🔄 正在测试登录API连接...');
    console.log('API地址:', import.meta.env.VITE_GLOB_API_URL);
    console.log('Mock模式:', import.meta.env.VITE_NITRO_MOCK);
    console.log('实际代理到:', import.meta.env.VITE_NITRO_MOCK === 'false' ? 'https://www.fb-hc-ads.click/mng/' : 'Mock服务');
    console.log('测试账号:', testCredentials.username);

    const result = await loginApi(testCredentials);
    
    return {
      success: true,
      message: '登录API连接成功',
      data: {
        hasAccessToken: !!result.accessToken,
        hasUserInfo: !!result.userInfo,
        hasPermissions: !!result.permissions,
        tokenLength: result.accessToken?.length || 0,
      },
    };
  } catch (error: any) {
    console.error('❌ 登录API测试失败:', error);
    
    return {
      success: false,
      message: `登录API连接失败: ${error.message || '未知错误'}`,
      error: {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message,
      },
    };
  }
}

/**
 * 测试用户信息API
 */
export async function testUserInfoApi(token?: string): Promise<LoginTestResult> {
  try {
    console.log('🔄 正在测试用户信息API...');
    
    const userInfo = await getUserInfoApi();
    
    return {
      success: true,
      message: '用户信息API连接成功',
      data: {
        userId: userInfo.userId,
        username: userInfo.username,
        realName: userInfo.realName,
        hasRoles: !!userInfo.roles?.length,
      },
    };
  } catch (error: any) {
    console.error('❌ 用户信息API测试失败:', error);
    
    return {
      success: false,
      message: `用户信息API连接失败: ${error.message || '未知错误'}`,
      error: {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message,
      },
    };
  }
}

/**
 * 测试权限码API
 */
export async function testAccessCodesApi(): Promise<LoginTestResult> {
  try {
    console.log('🔄 正在测试权限码API...');
    
    const accessCodes = await getAccessCodesApi();
    
    return {
      success: true,
      message: '权限码API连接成功',
      data: {
        codesCount: accessCodes.length,
        codes: accessCodes.slice(0, 5), // 只显示前5个权限码
      },
    };
  } catch (error: any) {
    console.error('❌ 权限码API测试失败:', error);
    
    return {
      success: false,
      message: `权限码API连接失败: ${error.message || '未知错误'}`,
      error: {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message,
      },
    };
  }
}

/**
 * 运行完整的登录流程测试
 */
export async function runFullLoginTest(): Promise<{
  loginTest: LoginTestResult;
  userInfoTest: LoginTestResult;
  accessCodesTest: LoginTestResult;
  summary: {
    totalTests: number;
    passedTests: number;
    failedTests: number;
    success: boolean;
  };
}> {
  console.log('🚀 开始运行完整登录流程测试...');
  
  const loginTest = await testLoginConnection();
  const userInfoTest = await testUserInfoApi();
  const accessCodesTest = await testAccessCodesApi();
  
  const tests = [loginTest, userInfoTest, accessCodesTest];
  const passedTests = tests.filter(test => test.success).length;
  const failedTests = tests.length - passedTests;
  
  const summary = {
    totalTests: tests.length,
    passedTests,
    failedTests,
    success: passedTests === tests.length,
  };
  
  console.log('📊 测试结果汇总:');
  console.log(`总测试数: ${summary.totalTests}`);
  console.log(`通过: ${summary.passedTests}`);
  console.log(`失败: ${summary.failedTests}`);
  console.log(`整体状态: ${summary.success ? '✅ 成功' : '❌ 失败'}`);
  
  return {
    loginTest,
    userInfoTest,
    accessCodesTest,
    summary,
  };
}

// 在开发环境下，将测试函数挂载到全局对象上，方便在控制台调用
if (import.meta.env.DEV) {
  (window as any).authTest = {
    testLoginConnection,
    testUserInfoApi,
    testAccessCodesApi,
    runFullLoginTest,
  };
  
  console.log('🔧 登录API测试工具已加载，可在控制台使用:');
  console.log('- window.authTest.testLoginConnection()');
  console.log('- window.authTest.testUserInfoApi()');
  console.log('- window.authTest.testAccessCodesApi()');
  console.log('- window.authTest.runFullLoginTest()');
}
