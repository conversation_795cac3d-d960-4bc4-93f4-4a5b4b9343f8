# 手动编辑配置文件详细指南

## 📁 配置文件位置
```
apps/web-admin/.env.development
```

## 🔧 当前配置内容
```env
# 端口号
VITE_PORT=5777

VITE_BASE=/

# 接口地址
# Mock模式：使用 /api
# 真实后台：使用 http://localhost:8080/api 或您的后台地址
VITE_GLOB_API_URL=/api

# 是否开启 Nitro Mock服务，true 为开启，false 为关闭
# 开发调试时保持 true，对接真实后台时改为 false
VITE_NITRO_MOCK=true

# 是否打开 devtools，true 为打开，false 为关闭
VITE_DEVTOOLS=false

# 是否注入全局loading
VITE_INJECT_APP_LOADING=true
```

## 🔄 切换操作步骤

### 场景1：从Mock模式切换到真实后台

#### 步骤1：打开配置文件
使用VS Code、记事本或任何文本编辑器打开：
```
apps/web-admin/.env.development
```

#### 步骤2：修改配置
找到这两行：
```env
VITE_GLOB_API_URL=/api
VITE_NITRO_MOCK=true
```

修改为：
```env
VITE_GLOB_API_URL=http://localhost:8080/api
VITE_NITRO_MOCK=false
```

#### 步骤3：保存文件
按 `Ctrl+S` 保存文件

#### 步骤4：重启开发服务器
在终端中：
```bash
# 停止当前服务器（按 Ctrl+C）
^C

# 重新启动
pnpm run dev
```

#### 步骤5：验证配置
在浏览器控制台运行：
```javascript
EnvSwitcher.getEnvInfo()
```

预期输出：
```javascript
{
  mode: "development",
  isMock: false,
  apiUrl: "http://localhost:8080/api",
  isDev: true,
  isProd: false
}
```

### 场景2：从真实后台切换回Mock模式

#### 修改配置：
```env
# 改回Mock模式
VITE_GLOB_API_URL=/api
VITE_NITRO_MOCK=true
```

## 🎯 常见配置示例

### 本地后台服务
```env
VITE_GLOB_API_URL=http://localhost:8080/api
VITE_NITRO_MOCK=false
```

### 局域网后台服务
```env
VITE_GLOB_API_URL=http://*************:8080/api
VITE_NITRO_MOCK=false
```

### 测试环境
```env
VITE_GLOB_API_URL=http://test-api.example.com/api
VITE_NITRO_MOCK=false
```

### 开发环境
```env
VITE_GLOB_API_URL=http://dev-api.example.com/api
VITE_NITRO_MOCK=false
```

### HTTPS接口
```env
VITE_GLOB_API_URL=https://api.example.com/api
VITE_NITRO_MOCK=false
```

## ⚠️ 注意事项

### 1. 文件编码
确保文件保存为 UTF-8 编码

### 2. 语法格式
- 等号两边不要有空格：`VITE_GLOB_API_URL=http://localhost:8080/api`
- 不要使用引号包围值
- 每行一个配置项

### 3. 重启要求
修改 `.env` 文件后必须重启开发服务器才能生效

### 4. 版本控制
建议将个人配置写入 `.env.local` 文件，避免提交到版本控制系统

## 🔍 故障排除

### 问题1：修改后配置不生效
**解决方案**：
1. 确认已保存文件
2. 重启开发服务器
3. 清除浏览器缓存

### 问题2：接口请求失败
**检查步骤**：
1. 确认后台服务是否启动
2. 检查API地址是否正确
3. 检查网络连接
4. 查看浏览器控制台错误信息

### 问题3：CORS跨域错误
**解决方案**：
1. 后台配置CORS允许
2. 或在前端配置代理

## 🛠️ 调试命令

在浏览器控制台使用以下命令进行调试：

```javascript
// 查看当前环境信息
EnvSwitcher.getEnvInfo()

// 测试后台连通性
EnvSwitcher.checkBackendConnection()

// 测试所有核心接口
ApiTester.testAllCoreApis()

// 显示切换指南
EnvSwitcher.showSwitchGuide()
```
