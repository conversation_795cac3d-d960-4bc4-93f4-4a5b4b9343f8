<script lang="ts" setup>
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { CouponApi } from '#/api';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { Button, message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { deleteUserCoupon, getUserCouponList } from '#/api';
import { $t } from '#/locales';

import { useUserCouponColumns, useUserCouponGridFormSchema } from './data';
import UserCouponForm from './modules/user-coupon-form.vue';

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: UserCouponForm,
  destroyOnClose: true,
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['createTime', ['startTime', 'endTime']]],
    schema: useUserCouponGridFormSchema(),
    submitOnChange: true,
  },
  gridOptions: {
    columns: useUserCouponColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          return await getUserCouponList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    exportConfig: {
      filename: '用户优惠券列表',
      sheetName: '用户优惠券数据',
      isHeader: true,
      isFooter: false,
      original: false,
      message: true,
      modes: ['current', 'selected'],
    },
    toolbarConfig: {
      custom: true,
      export: true,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<CouponApi.UserCoupon>,
});

function onActionClick(e: OnActionClickParams<CouponApi.UserCoupon>) {
  switch (e.code) {
    case 'delete': {
      onDelete(e.row);
      break;
    }
    case 'edit': {
      onEdit(e.row);
      break;
    }
  }
}

function onEdit(row: CouponApi.UserCoupon) {
  formDrawerApi.setData(row).open();
}

function onDelete(row: CouponApi.UserCoupon) {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting', [row.couponName]),
    duration: 0,
    key: 'action_process_msg',
  });
  deleteUserCoupon(row.id)
    .then(() => {
      message.success({
        content: $t('ui.actionMessage.deleteSuccess', [row.couponName]),
        key: 'action_process_msg',
      });
      onRefresh();
    })
    .catch(() => {
      hideLoading();
    });
}

function onRefresh() {
  gridApi.query();
}

function onCreate() {
  formDrawerApi.setData({}).open();
}
</script>
<template>
  <Page auto-content-height>
    <FormDrawer @success="onRefresh" />
    <Grid :table-title="$t('coupon.userCouponList')">
      <template #toolbar-tools>
        <Button type="primary" @click="onCreate">
          <Plus class="size-5" />
          {{ $t('ui.actionTitle.create', [`用户${$t('coupon.name')}`]) }}
        </Button>
      </template>
    </Grid>
  </Page>
</template>
