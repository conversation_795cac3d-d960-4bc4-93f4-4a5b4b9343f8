import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

export namespace VipApi {
  export interface VipLevel {
    [key: string]: any;
    id: string;
    icon?: string;
    levelName: string;
    levelValue: number;
    clickPrice: number;
    commissionDetail: string;
    purchasePrice: number;
    dataLimit: number;
    vipPrivileges: string;
    createTime?: string;
    updateTime?: string;
  }
}

/**
 * 获取VIP等级列表数据
 */
async function getVipLevelList(params: Recordable<any>) {
  return requestClient.get<Array<VipApi.VipLevel>>('/vip/list', { params });
}

/**
 * 创建VIP等级
 * @param data VIP等级数据
 */
async function createVipLevel(
  data: Omit<VipApi.VipLevel, 'createTime' | 'id' | 'updateTime'>,
) {
  return requestClient.post('/vip', data);
}

/**
 * 更新VIP等级
 *
 * @param id VIP等级 ID
 * @param data VIP等级数据
 */
async function updateVipLevel(
  id: string,
  data: Omit<VipApi.VipLevel, 'createTime' | 'id' | 'updateTime'>,
) {
  return requestClient.put(`/vip/${id}`, data);
}

/**
 * 删除VIP等级
 * @param id VIP等级 ID
 */
async function deleteVipLevel(id: string) {
  return requestClient.delete(`/vip/${id}`);
}

export { createVipLevel, deleteVipLevel, getVipLevelList, updateVipLevel };
