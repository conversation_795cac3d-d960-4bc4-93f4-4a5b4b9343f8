# 接口对接检查清单

## 📋 对接前准备

### 环境配置检查
- [ ] 后台接口地址已确认
- [ ] 网络连通性已测试
- [ ] CORS配置已处理
- [ ] SSL证书已配置（如使用HTTPS）

### 文档确认
- [ ] 接口文档已详细阅读
- [ ] 数据格式已确认
- [ ] 认证方式已了解
- [ ] 错误码定义已掌握

## 🔧 第一阶段：核心认证接口

### 登录接口 `/auth/login`
- [ ] 请求参数格式正确 `{username, password}`
- [ ] 响应数据格式符合预期 `{token, admin, permissions}`
- [ ] Token格式正确（Bearer Token）
- [ ] 登录成功后跳转正常
- [ ] 登录失败错误提示正确

### 获取用户信息 `/auth/profile`
- [ ] 接口调用成功
- [ ] 用户信息显示正确
- [ ] 权限信息获取正常
- [ ] 角色信息显示正确

### 退出登录 `/auth/logout`
- [ ] 退出接口调用成功
- [ ] Token清除正常
- [ ] 页面跳转到登录页
- [ ] 再次访问需要重新登录

### 修改密码 `/auth/change-password`
- [ ] 密码修改接口正常
- [ ] 参数验证正确
- [ ] 成功提示显示
- [ ] 修改后可正常登录

## 👥 第二阶段：管理员管理接口

### 角色管理
- [ ] 角色列表获取 `/admin/roles`
- [ ] 角色详情获取 `/admin/roles/{id}`
- [ ] 角色创建 `/admin/roles`
- [ ] 角色更新 `/admin/roles/{id}`
- [ ] 角色删除 `/admin/roles/{id}`
- [ ] 权限树获取 `/admin/permissions/tree`

### 管理员管理
- [ ] 管理员列表获取 `/admin/list`
- [ ] 管理员详情获取 `/admin/list/{id}`
- [ ] 管理员创建 `/admin/list`
- [ ] 管理员更新 `/admin/list/{id}`
- [ ] 密码重置 `/admin/list/{id}/reset-password`
- [ ] 管理员删除 `/admin/list/{id}`
- [ ] 登录日志查询 `/admin/list/{id}/login-logs`

## 🏢 第三阶段：核心业务接口

### 产品库管理
- [ ] 产品列表获取 `/products`
- [ ] 产品详情获取 `/products/{id}`
- [ ] 产品创建 `/products`
- [ ] 产品更新 `/products/{id}`
- [ ] 产品删除 `/products/{id}`
- [ ] 产品图片添加 `/products/{id}/images`
- [ ] 产品图片删除 `/products/images/{id}`

### 计划库管理
- [ ] 计划列表获取 `/plans`
- [ ] 计划详情获取 `/plans/{id}`
- [ ] 计划创建 `/plans`
- [ ] 计划更新 `/plans/{id}`
- [ ] 计划删除 `/plans/{id}`
- [ ] 计划状态修改 `/plans/{id}/status`

### 财务管理
#### 充值管理
- [ ] 充值记录列表 `/finance/recharge`
- [ ] 用户充值 `/finance/recharge`
- [ ] 批量充值 `/finance/recharge/batch`

#### 提现管理
- [ ] 提现记录列表 `/finance/withdraw`
- [ ] 提现详情获取 `/finance/withdraw/{id}`
- [ ] 提现审核 `/finance/withdraw/{id}/audit`

#### 交易记录
- [ ] 交易记录列表 `/finance/transactions`
- [ ] 交易详情获取 `/finance/transactions/{id}`
- [ ] 交易记录导出 `/finance/transactions/export`

### 优惠券管理
#### 优惠券管理
- [ ] 优惠券列表获取 `/coupons`
- [ ] 优惠券详情获取 `/coupons/{id}`
- [ ] 优惠券创建 `/coupons`
- [ ] 优惠券更新 `/coupons/{id}`
- [ ] 优惠券删除 `/coupons/{id}`
- [ ] 优惠券状态修改 `/coupons/{id}/status`

#### 用户优惠券
- [ ] 用户优惠券列表 `/user-coupons`
- [ ] 用户优惠券详情 `/user-coupons/{id}`
- [ ] 批量发放优惠券 `/user-coupons/batch`
- [ ] 用户优惠券状态更新 `/user-coupons/{id}/status`

## 🔄 第四阶段：扩展功能接口

### 代运营管理
- [ ] 代运营列表获取 `/agency-follows`
- [ ] 代运营详情获取 `/agency-follows/{id}`
- [ ] 代运营关系创建 `/agency-follows`
- [ ] 代运营状态更新 `/agency-follows/{id}/status`
- [ ] 代运营业绩统计 `/agency-follows/performance`

### 授权管理
- [ ] 授权码列表获取 `/auth-codes`
- [ ] 授权码详情获取 `/auth-codes/{id}`
- [ ] 授权码生成 `/auth-codes/generate`
- [ ] 授权码废弃 `/auth-codes/{id}/invalidate`
- [ ] 授权操作日志 `/auth-codes/logs`

### 用户计划管理
- [ ] 用户计划列表 `/user-plans`
- [ ] 用户计划详情 `/user-plans/{id}`
- [ ] 用户计划创建 `/user-plans`
- [ ] 用户计划更新 `/user-plans/{id}`
- [ ] 用户计划删除 `/user-plans/{id}`
- [ ] 用户计划状态更新 `/user-plans/{id}/status`

### 数据统计
- [ ] 注册统计 `/statistics/registration`
- [ ] 财务统计 `/statistics/financial`
- [ ] 用户统计 `/statistics/users`
- [ ] 业绩统计 `/statistics/performance`

### 用户反馈
- [ ] 反馈列表获取 `/feedback`
- [ ] 反馈详情获取 `/feedback/{id}`
- [ ] 反馈状态更新 `/feedback/{id}/status`
- [ ] 反馈回复 `/feedback/{id}/reply`

### 系统设置
- [ ] 系统配置获取 `/settings`
- [ ] 系统配置更新 `/settings`
- [ ] 通知管理 `/notifications`
- [ ] 文件上传 `/upload`

## 🧪 功能测试检查

### 数据展示
- [ ] 列表数据正确显示
- [ ] 分页功能正常
- [ ] 搜索筛选功能正常
- [ ] 排序功能正常
- [ ] 详情页数据完整

### 操作功能
- [ ] 创建操作成功
- [ ] 编辑操作成功
- [ ] 删除操作成功
- [ ] 批量操作正常
- [ ] 状态切换正常

### 错误处理
- [ ] 网络错误提示友好
- [ ] 权限错误处理正确
- [ ] 参数错误提示明确
- [ ] 服务器错误处理得当

### 用户体验
- [ ] 加载状态显示
- [ ] 操作反馈及时
- [ ] 成功提示清晰
- [ ] 错误提示友好

## 🔍 性能测试检查

### 响应时间
- [ ] 列表接口响应 < 2秒
- [ ] 详情接口响应 < 1秒
- [ ] 创建操作响应 < 3秒
- [ ] 更新操作响应 < 2秒
- [ ] 删除操作响应 < 1秒

### 并发测试
- [ ] 多用户同时操作正常
- [ ] 高频操作不出错
- [ ] 大数据量处理正常

## 🛡️ 安全测试检查

### 认证授权
- [ ] 未登录访问被拦截
- [ ] Token过期自动刷新
- [ ] 权限控制正确
- [ ] 敏感操作需要确认

### 数据安全
- [ ] 输入数据验证
- [ ] SQL注入防护
- [ ] XSS攻击防护
- [ ] 敏感信息脱敏

## 📊 最终验收标准

### 技术指标
- [ ] 所有接口对接完成
- [ ] 功能测试全部通过
- [ ] 性能指标达标
- [ ] 安全测试通过

### 业务指标
- [ ] 所有功能模块可用
- [ ] 数据流转正常
- [ ] 用户操作流畅
- [ ] 错误处理完善

### 文档完善
- [ ] 接口对接文档更新
- [ ] 问题解决方案记录
- [ ] 部署说明文档
- [ ] 用户使用手册

## 🚀 上线准备

### 环境准备
- [ ] 生产环境配置
- [ ] 数据库连接测试
- [ ] 缓存配置检查
- [ ] 监控告警设置

### 部署检查
- [ ] 代码版本确认
- [ ] 配置文件检查
- [ ] 依赖包版本确认
- [ ] 备份方案准备

### 应急预案
- [ ] 回滚方案准备
- [ ] 问题联系人确认
- [ ] 监控方案启用
- [ ] 日志收集配置

---

**检查完成率**: ___/总项数
**负责人**: ___________
**检查日期**: ___________
**备注**: ___________
