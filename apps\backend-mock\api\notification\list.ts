import { faker } from '@faker-js/faker';
import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, usePageResponseSuccess } from '~/utils/response';

const formatterCN = new Intl.DateTimeFormat('zh-CN', {
  timeZone: 'Asia/Shanghai',
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
});

const notificationTypes = ['system', 'promotion', 'reminder', 'warning'];

const notificationTitles = [
  '系统维护通知',
  '新功能上线公告',
  '账户安全提醒',
  '促销活动通知',
  '密码到期提醒',
  '系统升级通知',
  '重要公告',
  '服务条款更新',
  '优惠券到期提醒',
  '账户余额不足提醒',
];

const notificationContents = [
  '系统将于今晚23:00-01:00进行维护，期间可能影响正常使用。',
  '新版本已上线，增加了多项实用功能，欢迎体验。',
  '检测到您的账户在异地登录，请及时检查账户安全。',
  '限时促销活动开始，多款商品享受优惠价格。',
  '您的密码即将到期，请及时修改以确保账户安全。',
  '系统将进行重大升级，新增多项功能和性能优化。',
  '重要通知：平台政策调整，请仔细阅读相关条款。',
  '服务条款已更新，请查看最新版本并确认同意。',
  '您有优惠券即将到期，请及时使用以免过期。',
  '账户余额不足，请及时充值以免影响正常使用。',
];

function generateMockDataList(count: number) {
  const dataList = [];

  for (let i = 0; i < count; i++) {
    const createTime = faker.date.between({
      from: '2022-01-01',
      to: '2025-01-01',
    });
    const readStatus = faker.helpers.arrayElement([0, 1]);
    const readTime =
      readStatus === 1
        ? formatterCN.format(
            faker.date.between({ from: createTime, to: new Date() }),
          )
        : null;

    const dataItem: Record<string, any> = {
      id: faker.string.uuid(),
      userId: `U${faker.string.numeric(8)}`,
      notificationType: faker.helpers.arrayElement(notificationTypes),
      notificationTitle: faker.helpers.arrayElement(notificationTitles),
      notificationContent: faker.helpers.arrayElement(notificationContents),
      readStatus,
      readTime,
      updateTime: formatterCN.format(
        faker.date.between({ from: createTime, to: new Date() }),
      ),
      createTime: formatterCN.format(createTime),
    };

    dataList.push(dataItem);
  }

  return dataList;
}

const mockData = generateMockDataList(200);

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const {
    page = 1,
    pageSize = 20,
    userId,
    notificationType,
    readStatus,
    startTime,
    endTime,
  } = getQuery(event);

  let listData = structuredClone(mockData);

  if (userId) {
    listData = listData.filter((item) =>
      item.userId.toLowerCase().includes(String(userId).toLowerCase()),
    );
  }

  if (notificationType) {
    listData = listData.filter(
      (item) => item.notificationType === notificationType,
    );
  }

  if (['0', '1'].includes(readStatus as string)) {
    listData = listData.filter(
      (item) => item.readStatus === Number(readStatus),
    );
  }

  if (startTime) {
    listData = listData.filter((item) => item.createTime >= startTime);
  }

  if (endTime) {
    listData = listData.filter((item) => item.createTime <= endTime);
  }

  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
