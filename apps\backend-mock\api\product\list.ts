import { faker } from '@faker-js/faker';
import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, usePageResponseSuccess } from '~/utils/response';

const formatterCN = new Intl.DateTimeFormat('zh-CN', {
  timeZone: 'Asia/Shanghai',
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
});

const categories = [
  'electronics',
  'clothing',
  'food',
  'books',
  'home',
  'sports',
  'beauty',
  'automotive',
];

const companies = [
  '苹果科技有限公司',
  '华为技术有限公司',
  '小米科技有限公司',
  '阿里巴巴集团',
  '腾讯科技有限公司',
  '京东集团',
  '百度在线网络技术有限公司',
  '字节跳动科技有限公司',
  '美团点评',
  '滴滴出行科技有限公司',
];

function generateMockDataList(count: number) {
  const dataList = [];

  for (let i = 0; i < count; i++) {
    const category = faker.helpers.arrayElement(categories);
    const dataItem: Record<string, any> = {
      id: faker.string.uuid(),
      productCode: `P${faker.string.numeric(8)}`,
      productName: faker.commerce.productName(),
      productCategory: category,
      companyName: faker.helpers.arrayElement(companies),
      productDescription: faker.commerce.productDescription(),
      status: faker.helpers.arrayElement([0, 1]),
      productLogo: faker.image.avatar(),
      productMainImage: faker.image.url({ width: 400, height: 300 }),
      createTime: formatterCN.format(
        faker.date.between({ from: '2022-01-01', to: '2025-01-01' }),
      ),
    };

    dataList.push(dataItem);
  }

  return dataList;
}

const mockData = generateMockDataList(100);

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const {
    page = 1,
    pageSize = 20,
    productCode,
    productName,
    productCategory,
    companyName,
    startTime,
    endTime,
    status,
  } = getQuery(event);

  let listData = structuredClone(mockData);

  if (productCode) {
    listData = listData.filter((item) =>
      item.productCode
        .toLowerCase()
        .includes(String(productCode).toLowerCase()),
    );
  }

  if (productName) {
    listData = listData.filter((item) =>
      item.productName
        .toLowerCase()
        .includes(String(productName).toLowerCase()),
    );
  }

  if (productCategory) {
    listData = listData.filter(
      (item) => item.productCategory === productCategory,
    );
  }

  if (companyName) {
    listData = listData.filter((item) =>
      item.companyName
        .toLowerCase()
        .includes(String(companyName).toLowerCase()),
    );
  }

  if (startTime) {
    listData = listData.filter((item) => item.createTime >= startTime);
  }

  if (endTime) {
    listData = listData.filter((item) => item.createTime <= endTime);
  }

  if (['0', '1'].includes(status as string)) {
    listData = listData.filter((item) => item.status === Number(status));
  }

  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
