<template>
  <div class="app-info-editor">
    <div class="flex items-center justify-between mb-4">
      <h4 class="text-sm font-medium">{{ $t('product.appInfo') }}</h4>
      <div class="flex gap-2">
        <Button size="small" @click="addPresetFields">
          <Plus class="w-4 h-4 mr-1" />
          {{ $t('product.addPresetFields') }}
        </Button>
        <Button type="primary" size="small" @click="addItem">
          <Plus class="w-4 h-4 mr-1" />
          添加字段
        </Button>
      </div>
    </div>
    
    <div v-if="items.length === 0" class="text-gray-500 text-center py-4">
      {{ $t('product.noAppInfo') }}
    </div>
    
    <div v-else class="space-y-3">
      <div
        v-for="(item, index) in items"
        :key="item.id"
        class="flex items-center gap-2 p-3 border border-gray-200 rounded-lg"
      >
        <div class="flex-1">
          <Input
            v-model:value="item.key"
            :placeholder="$t('product.appInfoKeyPlaceholder')"
            size="small"
            @change="handleChange"
          />
        </div>
        <div class="flex-1">
          <Input
            v-model:value="item.value"
            :placeholder="$t('product.appInfoValuePlaceholder')"
            size="small"
            @change="handleChange"
          />
        </div>
        <Button
          type="text"
          danger
          size="small"
          @click="removeItem(index)"
        >
          <Delete class="w-4 h-4" />
        </Button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick } from 'vue';
import { Button, Input } from 'ant-design-vue';
import { Plus, Delete } from '@vben/icons';
import { $t } from '#/locales';
import type { ProductApi } from '#/api';

interface Props {
  modelValue?: ProductApi.AppInfo;
}

interface Emits {
  (e: 'update:modelValue', value: ProductApi.AppInfo): void;
  (e: 'change', value: ProductApi.AppInfo): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
});

const emits = defineEmits<Emits>();

// 内部状态
const items = ref<ProductApi.AppInfoItem[]>([]);

// 预设字段
const presetFields = [
  { key: 'Category', value: 'Simulation' },
  { key: 'Languages', value: 'English, Chinese, French, German, Italian' },
  { key: 'Age Rating', value: '12+' },
  { key: 'iOS Requires', value: 'iOS 13.0 or higher' },
  { key: 'Android Requires', value: '5.0 or higher' },
];

// 初始化数据
function initializeItems() {
  const appInfo = props.modelValue || {};
  items.value = Object.entries(appInfo).map(([key, value], index) => ({
    id: `app_info_${Date.now()}_${index}`,
    key,
    value: value || '',
  }));
  
  // 如果没有数据，添加一个空项
  if (items.value.length === 0) {
    addItem();
  }
}

// 添加新项
function addItem() {
  items.value.push({
    id: `app_info_${Date.now()}_${items.value.length}`,
    key: '',
    value: '',
  });
}

// 添加预设字段
function addPresetFields() {
  const existingKeys = new Set(items.value.map(item => item.key));

  presetFields.forEach(preset => {
    if (!existingKeys.has(preset.key)) {
      items.value.push({
        id: `app_info_${Date.now()}_${items.value.length}`,
        key: preset.key,
        value: preset.value,
      });
    }
  });

  handleChange();
}

// 删除项
function removeItem(index: number) {
  items.value.splice(index, 1);
  handleChange();
}

// 处理变更
function handleChange() {
  nextTick(() => {
    const result: ProductApi.AppInfo = {};
    
    items.value.forEach(item => {
      if (item.key && item.key.trim()) {
        result[item.key.trim()] = item.value || '';
      }
    });
    
    emits('update:modelValue', result);
    emits('change', result);
  });
}

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newValue) => {
    // 避免循环更新
    const currentValue: ProductApi.AppInfo = {};
    items.value.forEach(item => {
      if (item.key && item.key.trim()) {
        currentValue[item.key.trim()] = item.value || '';
      }
    });
    
    const newValueStr = JSON.stringify(newValue || {});
    const currentValueStr = JSON.stringify(currentValue);
    
    if (newValueStr !== currentValueStr) {
      initializeItems();
    }
  },
  { immediate: true, deep: true }
);

// 暴露方法给父组件
defineExpose({
  validate: () => {
    // 检查是否有重复的key
    const keys = items.value
      .map(item => item.key?.trim())
      .filter(key => key);
    
    const uniqueKeys = new Set(keys);
    if (keys.length !== uniqueKeys.size) {
      return {
        valid: false,
        message: $t('product.duplicateAppInfoKey'),
      };
    }
    
    return { valid: true };
  },
  getItems: () => items.value,
});
</script>

<style scoped>
.app-info-editor {
  @apply w-full;
}
</style>
