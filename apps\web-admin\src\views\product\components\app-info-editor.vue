<template>
  <div class="app-info-editor">
    <div class="flex items-center justify-between mb-4">
      <h4 class="text-sm font-medium">{{ $t('product.appInfo') }}</h4>
      <Button type="primary" size="small" @click="addItem">
        <Plus class="w-4 h-4 mr-1" />
        添加应用信息
      </Button>
    </div>

    <Table
      :columns="columns"
      :data-source="items"
      :pagination="false"
      size="small"
      bordered
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'key'">
          <Input
            v-model:value="record.key"
            placeholder="请输入标题"
            size="small"
            @input="handleInputChange"
            @blur="handleChange"
          />
        </template>
        <template v-else-if="column.key === 'value'">
          <Input
            v-model:value="record.value"
            placeholder="请输入内容"
            size="small"
            @input="handleInputChange"
            @blur="handleChange"
          />
        </template>
        <template v-else-if="column.key === 'operation'">
          <Button
            type="link"
            danger
            size="small"
            @click="removeItem(index)"
          >
            删除
          </Button>
        </template>
      </template>
    </Table>

    <div v-if="items.length === 0" class="text-center py-8 text-gray-500">
      暂无应用信息，点击上方按钮添加
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick, computed } from 'vue';
import { Button, Input, Table } from 'ant-design-vue';
import { Plus } from '@vben/icons';
import { $t } from '#/locales';
import type { ProductApi } from '#/api/product';

interface Props {
  modelValue?: ProductApi.AppInfo;
}

interface Emits {
  (e: 'update:modelValue', value: ProductApi.AppInfo): void;
  (e: 'change', value: ProductApi.AppInfo): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
});

const emits = defineEmits<Emits>();

// 内部状态
const items = ref<ProductApi.AppInfoItem[]>([]);

// 防抖定时器
let debounceTimer: NodeJS.Timeout | null = null;

// 表格列定义
const columns = computed(() => [
  {
    title: '标题',
    key: 'key',
    dataIndex: 'key',
    width: '40%',
  },
  {
    title: '内容',
    key: 'value',
    dataIndex: 'value',
    width: '45%',
  },
  {
    title: '操作',
    key: 'operation',
    width: '15%',
    align: 'center' as const,
  },
]);

// 初始化数据
function initializeItems() {
  const appInfo = props.modelValue || {};
  console.log('🔄 AppInfoEditor 初始化数据:', appInfo);

  items.value = Object.entries(appInfo).map(([key, value], index) => ({
    id: `app_info_${Date.now()}_${index}`,
    key,
    value: value || '',
  }));

  console.log('📋 AppInfoEditor 转换后的items:', items.value);
}

// 添加新项
function addItem() {
  try {
    const newItem = {
      id: `app_info_${Date.now()}_${items.value.length}`,
      key: '',
      value: '',
    };
    items.value.push(newItem);
    console.log('➕ AppInfoEditor 添加新项:', newItem);
  } catch (error) {
    console.error('❌ AppInfoEditor 添加项失败:', error);
  }
}

// 删除项
function removeItem(index: number) {
  try {
    if (index >= 0 && index < items.value.length) {
      items.value.splice(index, 1);
      handleChange();
    }
  } catch (error) {
    console.error('❌ AppInfoEditor 删除项失败:', error);
  }
}

// 处理输入变更（实时）
function handleInputChange() {
  // 防抖处理，避免频繁更新
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }

  debounceTimer = setTimeout(() => {
    handleChange();
  }, 300); // 300ms防抖
}

// 处理变更（失焦时或防抖触发）
function handleChange() {
  try {
    if (debounceTimer) {
      clearTimeout(debounceTimer);
      debounceTimer = null;
    }

    nextTick(() => {
      const result: ProductApi.AppInfo = {};

      items.value.forEach(item => {
        if (item.key && item.key.trim()) {
          result[item.key.trim()] = item.value || '';
        }
      });

      console.log('🔄 AppInfoEditor 数据变更:', result);
      emits('update:modelValue', result);
      emits('change', result);
    });
  } catch (error) {
    console.error('❌ AppInfoEditor 处理变更失败:', error);
  }
}

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newValue) => {
    // 避免循环更新
    const currentValue: ProductApi.AppInfo = {};
    items.value.forEach(item => {
      if (item.key && item.key.trim()) {
        currentValue[item.key.trim()] = item.value || '';
      }
    });
    
    const newValueStr = JSON.stringify(newValue || {});
    const currentValueStr = JSON.stringify(currentValue);
    
    if (newValueStr !== currentValueStr) {
      initializeItems();
    }
  },
  { immediate: true, deep: true }
);

// 暴露方法给父组件
defineExpose({
  validate: () => {
    // 检查是否有重复的key
    const keys = items.value
      .map(item => item.key?.trim())
      .filter(key => key);
    
    const uniqueKeys = new Set(keys);
    if (keys.length !== uniqueKeys.size) {
      return {
        valid: false,
        message: $t('product.duplicateAppInfoKey'),
      };
    }
    
    return { valid: true };
  },
  getItems: () => items.value,
});
</script>

<style scoped>
.app-info-editor {
  @apply w-full;
}
</style>
