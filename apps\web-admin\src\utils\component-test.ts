/**
 * 组件测试工具
 * 用于测试AppInfoEditor组件的加载和注册情况
 */

export interface ComponentTestResult {
  success: boolean;
  message: string;
  details?: any;
  error?: any;
}

/**
 * 测试AppInfoEditor组件是否正确注册
 */
export async function testAppInfoEditorRegistration(): Promise<ComponentTestResult> {
  try {
    console.log('🧪 测试AppInfoEditor组件注册...');
    
    // 1. 测试组件类型定义
    const componentModule = await import('#/adapter/component');
    const componentTypes = componentModule.default;
    
    console.log('📋 可用组件类型:', Object.keys(componentTypes));
    
    // 2. 检查AppInfoEditor是否在组件列表中
    const hasAppInfoEditor = 'AppInfoEditor' in componentTypes;
    console.log('🔍 AppInfoEditor是否注册:', hasAppInfoEditor);
    
    if (!hasAppInfoEditor) {
      return {
        success: false,
        message: 'AppInfoEditor组件未在adapter/component中注册',
        details: {
          availableComponents: Object.keys(componentTypes),
        },
      };
    }
    
    // 3. 尝试获取组件
    const AppInfoEditorComponent = componentTypes.AppInfoEditor;
    console.log('📦 AppInfoEditor组件:', AppInfoEditorComponent);
    
    // 4. 测试组件是否可以实例化
    if (typeof AppInfoEditorComponent === 'function' || typeof AppInfoEditorComponent === 'object') {
      return {
        success: true,
        message: 'AppInfoEditor组件注册成功',
        details: {
          componentType: typeof AppInfoEditorComponent,
          isAsyncComponent: AppInfoEditorComponent.__asyncResolved !== undefined,
          availableComponents: Object.keys(componentTypes),
        },
      };
    } else {
      return {
        success: false,
        message: 'AppInfoEditor组件类型异常',
        details: {
          componentType: typeof AppInfoEditorComponent,
          component: AppInfoEditorComponent,
        },
      };
    }
  } catch (error: any) {
    console.error('❌ AppInfoEditor组件测试失败:', error);
    
    return {
      success: false,
      message: `组件测试失败: ${error.message}`,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
    };
  }
}

/**
 * 测试组件文件是否可以正确导入
 */
export async function testAppInfoEditorImport(): Promise<ComponentTestResult> {
  try {
    console.log('🧪 测试AppInfoEditor组件导入...');
    
    // 直接导入组件文件
    const componentModule = await import('../views/product/components/app-info-editor.vue');
    
    console.log('📦 组件模块:', componentModule);
    
    if (componentModule.default) {
      return {
        success: true,
        message: 'AppInfoEditor组件文件导入成功',
        details: {
          hasDefault: !!componentModule.default,
          moduleKeys: Object.keys(componentModule),
          componentName: componentModule.default.name || componentModule.default.__name,
        },
      };
    } else {
      return {
        success: false,
        message: 'AppInfoEditor组件文件缺少默认导出',
        details: {
          moduleKeys: Object.keys(componentModule),
        },
      };
    }
  } catch (error: any) {
    console.error('❌ AppInfoEditor组件导入失败:', error);
    
    return {
      success: false,
      message: `组件导入失败: ${error.message}`,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
    };
  }
}

/**
 * 测试表单schema配置
 */
export async function testFormSchemaConfig(): Promise<ComponentTestResult> {
  try {
    console.log('🧪 测试表单schema配置...');
    
    // 导入表单schema
    const { useFormSchema } = await import('../views/product/data');
    const schema = useFormSchema();
    
    console.log('📋 表单schema:', schema);
    
    // 查找AppInfoEditor字段
    const appInfoField = schema.find(field => field.component === 'AppInfoEditor');
    
    if (appInfoField) {
      return {
        success: true,
        message: 'AppInfoEditor字段配置正确',
        details: {
          fieldConfig: appInfoField,
          totalFields: schema.length,
          allComponents: schema.map(field => field.component),
        },
      };
    } else {
      return {
        success: false,
        message: 'AppInfoEditor字段未在schema中找到',
        details: {
          totalFields: schema.length,
          allComponents: schema.map(field => field.component),
          allFieldNames: schema.map(field => field.fieldName),
        },
      };
    }
  } catch (error: any) {
    console.error('❌ 表单schema测试失败:', error);
    
    return {
      success: false,
      message: `表单schema测试失败: ${error.message}`,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
    };
  }
}

/**
 * 完整的组件测试
 */
export async function runFullComponentTest(): Promise<{
  registrationTest: ComponentTestResult;
  importTest: ComponentTestResult;
  schemaTest: ComponentTestResult;
  summary: {
    allPassed: boolean;
    passedCount: number;
    totalCount: number;
    issues: string[];
  };
}> {
  console.log('🚀 开始完整的AppInfoEditor组件测试...');
  
  const registrationTest = await testAppInfoEditorRegistration();
  const importTest = await testAppInfoEditorImport();
  const schemaTest = await testFormSchemaConfig();
  
  const tests = [registrationTest, importTest, schemaTest];
  const passedCount = tests.filter(test => test.success).length;
  const totalCount = tests.length;
  const allPassed = passedCount === totalCount;
  
  const issues: string[] = [];
  if (!registrationTest.success) issues.push('组件注册问题');
  if (!importTest.success) issues.push('组件导入问题');
  if (!schemaTest.success) issues.push('表单配置问题');
  
  console.log('📊 测试结果汇总:');
  console.log(`- 组件注册: ${registrationTest.success ? '✅' : '❌'}`);
  console.log(`- 组件导入: ${importTest.success ? '✅' : '❌'}`);
  console.log(`- 表单配置: ${schemaTest.success ? '✅' : '❌'}`);
  console.log(`- 总体结果: ${allPassed ? '✅ 全部通过' : `❌ ${passedCount}/${totalCount} 通过`}`);
  
  if (issues.length > 0) {
    console.log('🔧 需要修复的问题:', issues);
  }
  
  return {
    registrationTest,
    importTest,
    schemaTest,
    summary: {
      allPassed,
      passedCount,
      totalCount,
      issues,
    },
  };
}

// 在开发环境下，将测试函数挂载到全局对象上
if (import.meta.env.DEV) {
  (window as any).componentTest = {
    testAppInfoEditorRegistration,
    testAppInfoEditorImport,
    testFormSchemaConfig,
    runFullComponentTest,
  };
  
  console.log('🔧 组件测试工具已加载，可在控制台使用:');
  console.log('- window.componentTest.runFullComponentTest()');
  console.log('- window.componentTest.testAppInfoEditorRegistration()');
  console.log('- window.componentTest.testAppInfoEditorImport()');
  console.log('- window.componentTest.testFormSchemaConfig()');
}
