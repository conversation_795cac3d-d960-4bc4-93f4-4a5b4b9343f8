import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { $t } from '#/locales';

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择日期',
        style: { width: '100%' },
      },
      fieldName: 'date',
      label: $t('statistics.registration.date'),
    },
    {
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入注册人数',
        style: { width: '100%' },
        min: 0,
      },
      fieldName: 'registrationCount',
      label: $t('statistics.registration.registrationCount'),
    },
  ];
}

export function useColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'id',
      title: 'ID',
      width: 80,
    },
    {
      field: 'date',
      title: $t('statistics.registration.date'),
      width: 120,
    },
    {
      field: 'registrationCount',
      title: $t('statistics.registration.registrationCount'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) =>
            cellValue?.toLocaleString() || '0',
        },
      },
    },
    {
      field: 'updateTime',
      title: $t('statistics.registration.updateTime'),
      width: 180,
    },
    {
      field: 'createTime',
      title: $t('statistics.registration.createTime'),
      width: 180,
    },
  ];
}
