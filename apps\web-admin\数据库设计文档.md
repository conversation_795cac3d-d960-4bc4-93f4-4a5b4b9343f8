# 数据库设计文档

## 概述

本文档基于接口文档分析，为FBM项目提供数据库设计方案。该设计旨在满足项目中所有模块的数据需求，包括用户管理、产品管理、计划管理、钱包功能、代运营、通知系统等核心功能。

## 数据库选择建议

团队已决定使用MySQL作为关系型数据库，用于存储结构化数据和处理复杂查询。对于需要高性能读写的部分（如通知、缓存等），可以考虑使用Redis作为辅助数据存储。

### MySQL特定配置建议

1. **存储引擎**：使用InnoDB存储引擎，支持事务、行级锁定和外键约束
2. **字符集**：使用utf8mb4字符集，支持完整的Unicode字符（包括emoji表情）
3. **排序规则**：建议使用utf8mb4_general_ci或utf8mb4_unicode_ci
4. **自增主键**：所有表均采用BIGINT类型的自增主键，避免将来数据量大时的溢出问题
5. **外键约束**：对于重要的关系型表之间的关联，建议添加外键约束以保持数据一致性
6. **索引优化**：对常用查询条件创建合适的索引，提高查询性能

## 数据库命名规范

- 表名：使用小写字母，单词间用下划线分隔，采用复数形式（例如：users, products, plans）
- 字段名：使用小写字母，单词间用下划线分隔（例如：user_id, create_time）
- 主键：统一使用id作为主键名称
- 外键命名：{关联表名单数}\_id（例如：user_id, product_id）

## 通用字段

所有表都应包含以下字段：

| 字段名      | 类型      | 说明                             |
| ----------- | --------- | -------------------------------- |
| id          | BIGINT    | 主键，自增                       |
| create_time | TIMESTAMP | 创建时间，默认为当前时间         |
| update_time | TIMESTAMP | 更新时间，默认为当前时间         |
| is_deleted  | TINYINT   | 逻辑删除标志，0-未删除，1-已删除 |

## 目录

1. [用户模块](#用户模块)
2. [产品模块](#产品模块)
3. [计划模块](#计划模块)
4. [钱包模块](#钱包模块)
5. [代运营模块](#代运营模块)
6. [广告中心模块](#广告中心模块)
7. [通知模块](#通知模块)
8. [推广模块](#推广模块)
9. [反馈模块](#反馈模块)
10. [系统管理模块](#系统管理模块)
11. [授权管理模块](#授权管理模块)
12. [表关系图](#表关系图)

## 用户模块

### users 表

存储用户基本信息。

| 字段名           | 类型         | 允许为空 | 默认值 | 说明                     |
| ---------------- | ------------ | -------- | ------ | ------------------------ |
| id               | BIGINT       | 否       |        | 主键，自增               |
| username         | VARCHAR(64)  | 否       |        | 用户名                   |
| fb_id            | VARCHAR(64)  | 否       |        | Facebook ID              |
| auth_code        | VARCHAR(128) | 是       | NULL   | 授权码(关联auth_codes表) |
| fb_nickname      | VARCHAR(64)  | 是       | NULL   | Facebook昵称             |
| fb_avatar        | VARCHAR(255) | 是       | NULL   | Facebook头像URL          |
| fb_lang          | VARCHAR(16)  | 是       | NULL   | Facebook语言设置         |
| fb_time_zone     | VARCHAR(32)  | 是       | NULL   | Facebook时区             |
| vip_level        | TINYINT      | 否       | 0      | VIP等级                  |
| credit_code      | VARCHAR(16)  | 是       | NULL   | 信用代码                 |
| star_level       | TINYINT      | 否       | 0      | 星级                     |
| invite_code      | VARCHAR(16)  | 否       |        | 邀请码                   |
| agency_id        | VARCHAR(64)  | 是       | NULL   | 代运营ID                 |
| payment_pwd_hash | VARCHAR(255) | 是       | NULL   | 支付密码哈希值           |
| payment_pwd_salt | VARCHAR(64)  | 是       | NULL   | 支付密码盐值             |
| admin_id         | BIGINT       | 是       | NULL   | 所属管理员ID             |
| status           | VARCHAR(16)  | 否       | '启用' | 用户状态(启用,禁用)      |
| last_login_time  | TIMESTAMP    | 是       | NULL   | 最后登录时间             |
| create_time      | TIMESTAMP    | 否       | NOW()  | 创建时间                 |
| update_time      | TIMESTAMP    | 否       | NOW()  | 更新时间                 |
| is_deleted       | TINYINT      | 否       | 0      | 是否删除，0-否，1-是     |

索引：

- PRIMARY KEY (`id`)
- UNIQUE KEY `idx_fb_id` (`fb_id`)
- UNIQUE KEY `idx_invite_code` (`invite_code`)
- UNIQUE KEY `idx_auth_code` (`auth_code`)
- INDEX `idx_agency_id` (`agency_id`)
- INDEX `idx_admin_id` (`admin_id`)
- INDEX `idx_status` (`status`)

## 产品模块

### products 表

存储产品信息。

| 字段名           | 类型         | 允许为空 | 默认值 | 说明                  |
| ---------------- | ------------ | -------- | ------ | --------------------- |
| id               | BIGINT       | 否       |        | 主键，自增            |
| product_code     | VARCHAR(32)  | 否       |        | 产品编码，格式如PROD_001  |
| name             | VARCHAR(128) | 否       |        | 产品名称              |
| category         | VARCHAR(128) | 否       |        | 产品分类              |
| company          | VARCHAR(128) | 否       |        | 公司名称              |
| description      | TEXT         | 否       |        | 产品简介              |
| status           | VARCHAR(16)  | 否       | '生效' | 产品状态(生效,下架等) |
| logo             | VARCHAR(255) | 否       |        | 产品Logo URL          |
| image            | VARCHAR(255) | 否       |        | 产品主图URL           |
| google_play_link | VARCHAR(255) | 是       | NULL   | Google Play商店链接   |
| app_store_link   | VARCHAR(255) | 是       | NULL   | App Store商店链接     |
| app_info         | TEXT         | 是       | NULL   | 应用信息(JSON格式)    |
| create_time      | TIMESTAMP    | 否       | NOW()  | 创建时间              |
| update_time      | TIMESTAMP    | 否       | NOW()  | 更新时间              |
| is_deleted       | TINYINT      | 否       | 0      | 是否删除，0-否，1-是  |

索引：

- PRIMARY KEY (`id`)
- UNIQUE KEY `idx_product_code` (`product_code`)
- INDEX `idx_name` (`name`)
- INDEX `idx_category` (`category`)
- INDEX `idx_company` (`company`)
- INDEX `idx_status` (`status`)

### app_info 字段JSON结构说明

app_info字段存储应用的详细信息，使用JSON格式，包含以下结构：

```json
{
  "category": "Game", // 应用分类
  "languages": "English and 13 more", // 支持的语言
  "age_rating": "9+", // 年龄评级
  "ios_requires": "12+", // iOS系统要求
  "android_requires": "7.0 and up" // Android系统要求
}
```

其他可能的字段包括版本号、应用大小、安装次数、评分等。

### product_images 表

存储产品相关图片(如截图等)。

| 字段名      | 类型         | 允许为空 | 默认值 | 说明                           |
| ----------- | ------------ | -------- | ------ | ------------------------------ |
| id          | BIGINT       | 否       |        | 主键，自增                     |
| product_id  | BIGINT       | 否       |        | 产品ID，关联products表的id字段 |
| image_url   | VARCHAR(255) | 否       |        | 图片URL路径            |
| image_type  | VARCHAR(32)  | 否       |        | 图片类型(logo,截图等)  |
| sort_order  | INT          | 否       | 0      | 排序顺序               |
| create_time | TIMESTAMP    | 否       | NOW()  | 创建时间               |
| update_time | TIMESTAMP    | 否       | NOW()  | 更新时间               |
| is_deleted  | TINYINT      | 否       | 0      | 是否删除，0-否，1-是   |

索引：

- PRIMARY KEY (`id`)
- INDEX `idx_product_id` (`product_id`)
- INDEX `idx_image_type` (`image_type`)

## 计划模块

### plans 表

存储广告计划基本信息。

| 字段名            | 类型         | 允许为空 | 默认值 | 说明                   |
| ----------------- | ------------ | -------- | ------ | ---------------------- |
| id                | BIGINT       | 否       |        | 主键，自增             |
| plan_code         | VARCHAR(64)  | 否       |        | 计划编码，格式如P_12345678 |
| product_id        | BIGINT       | 否       |        | 产品ID，关联products表 |
| name              | VARCHAR(128) | 否       |        | 计划名称               |
| description       | TEXT         | 否       |        | 计划简介               |
| main_image        | VARCHAR(255) | 否       |        | 主图URL                |
| status            | VARCHAR(16)  | 否       | '激活' | 状态(激活,未激活等)    |
| promotion_content | TEXT         | 是       | NULL   | 投放内容(JSON格式)     |
| placement_rules   | TEXT         | 是       | NULL   | 投放规则(JSON格式)     |
| audience_criteria | TEXT         | 是       | NULL   | 用户定向条件(JSON格式) |
| create_time       | TIMESTAMP    | 否       | NOW()  | 创建时间               |
| update_time       | TIMESTAMP    | 否       | NOW()  | 更新时间               |
| is_deleted        | TINYINT      | 否       | 0      | 是否删除，0-否，1-是   |

索引：

- PRIMARY KEY (`id`)
- UNIQUE KEY `idx_plan_code` (`plan_code`)
- INDEX `idx_product_id` (`product_id`)
- INDEX `idx_status` (`status`)
- INDEX `idx_create_time` (`create_time`)

### promotion_content 字段JSON结构说明

promotion_content字段存储投放内容的详细信息，使用JSON格式，包含以下结构：

```json
[
  {
    "title": "Promotional Link",
    "content": "https://example.com/product"
  },
  {
    "title": "Ad Display Format",
    "content": "Image"
  },
  {
    "title": "Conversion Method",
    "content": "Landing Page"
  }
]
```

### placement_rules 字段JSON结构说明

placement_rules字段存储投放规则的详细信息，使用JSON格式，包含以下结构：

```json
[
  {
    "title": "Ad Placement Strategy",
    "content": "CPM"
  },
  {
    "title": "Ad Placement Cost",
    "content": "Smart Optimization"
  },
  {
    "title": "Payment Method",
    "content": "CPC"
  }
]
```

### audience_criteria 字段JSON结构说明

audience_criteria字段存储用户定向条件的详细信息，使用JSON格式，包含以下结构：

```json
[
  {
    "title": "Location",
    "content": "Global"
  }
]
```

### user_plans 表

存储用户计划信息，关联用户和计划。

| 字段名              | 类型          | 允许为空 | 默认值   | 说明                     |
| ------------------- | ------------- | -------- | -------- | ------------------------ |
| id                  | BIGINT        | 否       |          | 主键，自增               |
| plan_id             | VARCHAR(64)   | 否       |          | 计划ID，关联plans表      |
| user_id             | BIGINT        | 否       |          | 用户ID，关联users表      |
| click_price         | DECIMAL(10,4) | 否       |          | 适用点击单价             |
| vip_level           | TINYINT       | 否       | 0        | 用户VIP等级              |
| impression_count    | INT           | 否       | 0        | 展示数                   |
| click_count         | INT           | 否       | 0        | 点击数                   |
| min_amount          | DECIMAL(10,2) | 否       | 0.00     | 最低投放金额             |
| max_amount          | DECIMAL(10,2) | 否       | 0.00     | 最高投放金额             |
| invested_amount     | DECIMAL(10,2) | 否       | 0.00     | 已投放金额               |
| consumed_amount     | DECIMAL(10,2) | 否       | 0.00     | 已消费金额               |
| placement_status    | VARCHAR(16)   | 否       | '投放中' | 投放状态(投放中,暂停等)  |
| consumption_minutes | INT           | 否       | 1        | 消耗时间(分钟)           |
| failure_notes       | TEXT          | 是       | NULL     | 失败备注                 |
| is_official         | TINYINT       | 否       | 0        | 是否官方推荐，0-否，1-是 |
| create_time         | TIMESTAMP     | 否       | NOW()    | 创建时间                 |
| update_time         | TIMESTAMP     | 否       | NOW()    | 更新时间                 |
| is_deleted          | TINYINT       | 否       | 0        | 是否删除，0-否，1-是     |

索引：

- PRIMARY KEY (`id`)
- UNIQUE KEY `idx_plan_user` (`plan_id`, `user_id`)
- INDEX `idx_plan_id` (`plan_id`)
- INDEX `idx_user_id` (`user_id`)
- INDEX `idx_placement_status` (`placement_status`)
- INDEX `idx_create_time` (`create_time`)

### user_plan_vip_prices 表

存储用户计划的VIP等级价格配置。

| 字段名       | 类型          | 允许为空 | 默认值 | 说明                         |
| ------------ | ------------- | -------- | ------ | ---------------------------- |
| id           | BIGINT        | 否       |        | 主键，自增                   |
| user_plan_id | BIGINT        | 否       |        | 用户计划ID，关联user_plans表 |
| vip_level    | TINYINT       | 否       |        | VIP等级                      |
| price        | DECIMAL(10,4) | 否       |        | 单价                         |
| is_current   | TINYINT       | 否       | 0      | 是否当前使用，0-否，1-是     |
| create_time  | TIMESTAMP     | 否       | NOW()  | 创建时间                     |
| update_time  | TIMESTAMP     | 否       | NOW()  | 更新时间                     |
| is_deleted   | TINYINT       | 否       | 0      | 是否删除，0-否，1-是         |

索引：

- PRIMARY KEY (`id`)
- INDEX `idx_user_plan_id` (`user_plan_id`)
- UNIQUE KEY `idx_user_plan_vip` (`user_plan_id`, `vip_level`)

## 钱包模块

### wallets 表

存储用户钱包基本信息。

| 字段名              | 类型          | 允许为空 | 默认值 | 说明                 |
| ------------------- | ------------- | -------- | ------ | -------------------- |
| id                  | BIGINT        | 否       |        | 主键，自增           |
| user_id             | BIGINT        | 否       |        | 用户ID，关联users表  |
| total_assets        | DECIMAL(12,2) | 否       | 0.00   | 总资产               |
| available_balance   | DECIMAL(12,2) | 否       | 0.00   | 可用余额             |
| pending_consumption | DECIMAL(12,2) | 否       | 0.00   | 待消费金额           |
| pending_settlement  | DECIMAL(12,2) | 否       | 0.00   | 待结算金额           |
| currency            | VARCHAR(16)   | 否       | 'USD'  | 货币类型             |
| create_time         | TIMESTAMP     | 否       | NOW()  | 创建时间             |
| update_time         | TIMESTAMP     | 否       | NOW()  | 更新时间             |
| is_deleted          | TINYINT       | 否       | 0      | 是否删除，0-否，1-是 |

索引：

- PRIMARY KEY (`id`)
- UNIQUE KEY `idx_user_id` (`user_id`)

### wallet_transactions 表

存储钱包交易记录。

| 字段名           | 类型          | 允许为空 | 默认值      | 说明                           |
| ---------------- | ------------- | -------- | ----------- | ------------------------------ |
| id               | BIGINT        | 否       |             | 主键，自增                     |
| user_id          | BIGINT        | 否       |             | 用户ID，关联users表            |
| type             | VARCHAR(32)   | 否       |             | 交易类型                       |
| amount           | DECIMAL(12,2) | 否       |             | 交易金额                       |
| balance_before   | DECIMAL(12,2) | 否       |             | 交易前余额                     |
| balance_after    | DECIMAL(12,2) | 否       |             | 交易后余额                     |
| transaction_code | VARCHAR(16)   | 是       | NULL        | 交易编码                       |
| remark           | VARCHAR(255)  | 是       | NULL        | 备注                           |
| related_id       | VARCHAR(64)   | 是       | NULL        | 关联ID（如订单ID）             |
| status           | VARCHAR(16)   | 否       | 'completed' | 状态(pending,completed,failed) |
| create_time      | TIMESTAMP     | 否       | NOW()       | 创建时间                       |
| update_time      | TIMESTAMP     | 否       | NOW()       | 更新时间                       |
| is_deleted       | TINYINT       | 否       | 0           | 是否删除，0-否，1-是           |

索引：

- PRIMARY KEY (`id`)
- INDEX `idx_user_id` (`user_id`)
- INDEX `idx_type` (`type`)
- INDEX `idx_create_time` (`create_time`)
- INDEX `idx_related_id` (`related_id`)

### user_payment_methods 表

存储用户提现方式信息（银行卡或加密货币地址）。

| 字段名       | 类型         | 允许为空 | 默认值   | 说明                           |
| ------------ | ------------ | -------- | -------- | ------------------------------ |
| id           | BIGINT       | 否       |          | 主键，自增                     |
| payment_code | VARCHAR(32)  | 否       |          | 支付方式编码，如PAY_001        |
| user_id      | BIGINT       | 否       |          | 用户ID，关联users表            |
| type         | VARCHAR(16)  | 否       |          | 类型(bank_card,crypto)         |
| name         | VARCHAR(64)  | 否       |          | 名称（银行名称或加密货币名称） |
| account_name | VARCHAR(64)  | 是       | NULL     | 账户名称（持卡人姓名）         |
| account_no   | VARCHAR(64)  | 否       |          | 账号（卡号或地址，加密存储）   |
| masked_no    | VARCHAR(32)  | 否       |          | 掩码账号（显示用）             |
| branch       | VARCHAR(128) | 是       | NULL     | 支行名称（银行卡时使用）       |
| network      | VARCHAR(32)  | 是       | NULL     | 网络类型（加密货币时使用）     |
| currency     | VARCHAR(16)  | 否       | 'USD'    | 货币类型                       |
| status       | VARCHAR(16)  | 否       | 'active' | 状态(active,inactive)          |
| create_time  | TIMESTAMP    | 否       | NOW()    | 创建时间                       |
| update_time  | TIMESTAMP    | 否       | NOW()    | 更新时间                       |
| is_deleted   | TINYINT      | 否       | 0        | 是否删除，0-否，1-是           |

索引：

- PRIMARY KEY (`id`)
- UNIQUE KEY `idx_payment_code` (`payment_code`)
- UNIQUE KEY `idx_user_id` (`user_id`)
- INDEX `idx_type` (`type`)
- INDEX `idx_status` (`status`)

### withdrawals 表

存储用户提现申请记录。

| 字段名         | 类型          | 允许为空 | 默认值    | 说明                                      |
| -------------- | ------------- | -------- | --------- | ----------------------------------------- |
| id             | BIGINT        | 否       |           | 主键，自增                                |
| withdrawal_code| VARCHAR(32)   | 否       |           | 提现编码，如W2025041901                    |
| user_id        | BIGINT        | 否       |           | 用户ID，关联users表                       |
| amount         | DECIMAL(12,2) | 否       |           | 提现金额                                  |
| status         | VARCHAR(32)   | 否       | 'pending' | 状态(pending,processing,completed,failed) |
| transaction_id | BIGINT        | 是       | NULL      | 关联交易ID                                |
| remark         | VARCHAR(255)  | 是       | NULL      | 备注                                      |
| request_time   | TIMESTAMP     | 否       | NOW()     | 请求时间                                  |
| complete_time  | TIMESTAMP     | 是       | NULL      | 完成时间                                  |
| create_time    | TIMESTAMP     | 否       | NOW()     | 创建时间                                  |
| update_time    | TIMESTAMP     | 否       | NOW()     | 更新时间                                  |
| is_deleted     | TINYINT       | 否       | 0         | 是否删除，0-否，1-是                      |

索引：

- PRIMARY KEY (`id`)
- UNIQUE KEY `idx_withdrawal_code` (`withdrawal_code`)
- INDEX `idx_user_id` (`user_id`)
- INDEX `idx_status` (`status`)
- INDEX `idx_request_time` (`request_time`)

## 代运营模块

### agency_follows 表

存储用户跟随代运营的记录。

| 字段名      | 类型        | 允许为空 | 默认值    | 说明                       |
| ----------- | ----------- | -------- | --------- | -------------------------- |
| id          | BIGINT      | 否       |           | 主键，自增                 |
| follow_code | VARCHAR(32) | 否       |           | 跟随编码，如FOLLOW_001     |
| user_id     | BIGINT      | 否       |           | 用户ID，关联users表        |
| agency_id   | VARCHAR(64) | 否       |           | 代运营ID                   |
| status      | VARCHAR(16) | 否       | 'pending' | 状态(pending,active,ended) |
| start_time  | TIMESTAMP   | 是       | NULL      | 开始时间                   |
| end_time    | TIMESTAMP   | 是       | NULL      | 结束时间                   |
| create_time | TIMESTAMP   | 否       | NOW()     | 创建时间                   |
| update_time | TIMESTAMP   | 否       | NOW()     | 更新时间                   |
| is_deleted  | TINYINT     | 否       | 0         | 是否删除，0-否，1-是       |

索引：

- PRIMARY KEY (`id`)
- UNIQUE KEY `idx_follow_code` (`follow_code`)
- INDEX `idx_user_id` (`user_id`)
- INDEX `idx_agency_id` (`agency_id`)
- INDEX `idx_status` (`status`)

### agency_follow_performance 表

存储代运营跟随的业绩表现。

| 字段名      | 类型          | 允许为空 | 默认值 | 说明                                 |
| ----------- | ------------- | -------- | ------ | ------------------------------------ |
| id          | BIGINT        | 否       |        | 主键，自增                           |
| follow_id   | BIGINT        | 否       |        | 跟随ID，关联agency_follows表的id字段 |
| roi         | DECIMAL(10,2) | 是       | NULL   | 投资回报率                           |
| spend       | DECIMAL(12,2) | 是       | NULL   | 花费金额                             |
| revenue     | DECIMAL(12,2) | 是       | NULL   | 收入金额                             |
| create_time | TIMESTAMP     | 否       | NOW()  | 创建时间                             |
| update_time | TIMESTAMP     | 否       | NOW()  | 更新时间                             |
| is_deleted  | TINYINT       | 否       | 0      | 是否删除，0-否，1-是                 |

索引：

- PRIMARY KEY (`id`)
- UNIQUE KEY `idx_follow_id` (`follow_id`)

## 广告中心模块

> 注意：广告统计数据(如展示次数、点击次数、收入、支出等)不单独存储在表中，而是通过user_plans表、wallets表和wallet_transactions表实时计算得出，避免数据冗余和一致性问题。这种设计使统计数据始终保持最新状态，并能按不同维度(日/周/月)动态聚合。

## 通知模块

### notifications 表

存储用户消息通知。

| 字段名       | 类型         | 允许为空 | 默认值    | 说明                               |
| ------------ | ------------ | -------- | --------- | ---------------------------------- |
| id           | BIGINT       | 否       |           | 主键，自增                         |
| notice_code  | VARCHAR(32)  | 否       |           | 通知编码，如NOTE001                |
| user_id      | BIGINT       | 否       |           | 用户ID，关联users表                |
| title        | VARCHAR(128) | 否       |           | 通知标题                           |
| content      | TEXT         | 否       |           | 通知内容                           |
| type         | VARCHAR(32)  | 否       |           | 类型(system,maintenance,activity)  |
| status       | VARCHAR(16)  | 否       | 'pending' | 状态(pending,processing,completed) |
| is_read      | TINYINT      | 否       | 0         | 是否已读，0-否，1-是               |
| link         | VARCHAR(255) | 是       | NULL      | 相关链接                           |
| expire_time  | TIMESTAMP    | 是       | NULL      | 过期时间                           |
| create_time  | TIMESTAMP    | 否       | NOW()     | 创建时间                           |
| update_time  | TIMESTAMP    | 否       | NOW()     | 更新时间                           |
| is_deleted   | TINYINT      | 否       | 0         | 是否删除，0-否，1-是               |

索引：

- PRIMARY KEY (`id`)
- UNIQUE KEY `idx_notice_code` (`notice_code`)
- INDEX `idx_user_id` (`user_id`)
- INDEX `idx_type` (`type`)
- INDEX `idx_status` (`status`)
- INDEX `idx_is_read` (`is_read`)
- INDEX `idx_create_time` (`create_time`)

## 推广模块

### coupons 表

存储优惠券基本信息。

| 字段名          | 类型          | 允许为空 | 默认值 | 说明                                         |
| --------------- | ------------- | -------- | ------ | -------------------------------------------- |
| id              | BIGINT        | 否       |        | 主键，自增                                   |
| coupon_code     | VARCHAR(32)   | 否       |        | 优惠券编码，如COUPON_001                     |
| name            | VARCHAR(64)   | 否       |        | 优惠券名称                                   |
| code            | VARCHAR(32)   | 否       |        | 优惠券代码                                   |
| type            | VARCHAR(16)   | 否       |        | 类型(增值券,抵扣券,团队券,自定义,固定金额券) |
| discount_type   | VARCHAR(16)   | 否       |        | 折扣类型(固定金额,百分比)                    |
| value           | DECIMAL(10,2) | 否       |        | 优惠券价值                                   |
| min_spend       | DECIMAL(10,2) | 否       | 0.00   | 最低消费金额                                 |
| max_discount    | DECIMAL(10,2) | 是       | NULL   | 最高优惠金额（百分比优惠时）                 |
| description     | VARCHAR(255)  | 是       | NULL   | 优惠券描述                                   |
| status          | VARCHAR(16)   | 否       | '启用' | 状态(启用,禁用)                              |
| is_new_user     | TINYINT       | 否       | 0      | 是否新人券，0-否，1-是                       |
| limit_count     | INT           | 否       | 1      | 每个用户限领数量                             |
| image_url       | VARCHAR(255)  | 是       | NULL   | 优惠券图片URL                                |
| start_time      | TIMESTAMP     | 否       |        | 开始时间                                     |
| end_time        | TIMESTAMP     | 否       |        | 结束时间                                     |
| total_quantity  | INT           | 否       | 0      | 总数量（0表示无限）                          |
| remain_quantity | INT           | 否       | 0      | 剩余数量                                     |
| admin_id        | BIGINT        | 是       | NULL   | 创建管理员ID                                 |
| create_time     | TIMESTAMP     | 否       | NOW()  | 创建时间                                     |
| update_time     | TIMESTAMP     | 否       | NOW()  | 更新时间                                     |
| is_deleted      | TINYINT       | 否       | 0      | 是否删除，0-否，1-是                         |

索引：

- PRIMARY KEY (`id`)
- UNIQUE KEY `idx_coupon_code` (`coupon_code`)
- UNIQUE KEY `idx_code` (`code`)
- INDEX `idx_status` (`status`)
- INDEX `idx_type` (`type`)
- INDEX `idx_is_new_user` (`is_new_user`)
- INDEX `idx_start_time` (`start_time`)
- INDEX `idx_end_time` (`end_time`)

### user_coupons 表

存储用户优惠券关联信息。

| 字段名          | 类型          | 允许为空 | 默认值   | 说明                          |
| --------------- | ------------- | -------- | -------- | ----------------------------- |
| id              | BIGINT        | 否       |          | 主键，自增                    |
| user_id         | BIGINT        | 否       |          | 用户ID，关联users表           |
| coupon_id       | BIGINT        | 否       |          | 优惠券ID，关联coupons表的id字段 |
| status          | VARCHAR(16)   | 否       | 'unused' | 状态(unused,used,expired)     |
| used_time       | TIMESTAMP     | 是       | NULL     | 使用时间                      |
| claim_time      | TIMESTAMP     | 否       | NOW()    | 领取时间                      |
| order_id        | VARCHAR(64)   | 是       | NULL     | 使用订单ID                    |
| discount_amount | DECIMAL(10,2) | 是       | NULL     | 优惠金额                      |
| expired_time    | TIMESTAMP     | 否       |          | 过期时间                      |
| create_time     | TIMESTAMP     | 否       | NOW()    | 创建时间                      |
| update_time     | TIMESTAMP     | 否       | NOW()    | 更新时间                      |
| is_deleted      | TINYINT       | 否       | 0        | 是否删除，0-否，1-是           |

索引：

- PRIMARY KEY (`id`)
- INDEX `idx_user_id` (`user_id`)
- INDEX `idx_coupon_id` (`coupon_id`)
- INDEX `idx_status` (`status`)
- INDEX `idx_claim_time` (`claim_time`)
- INDEX `idx_expired_time` (`expired_time`)

## 反馈模块

### feedbacks 表

存储用户反馈意见。

| 字段名      | 类型         | 允许为空 | 默认值    | 说明                               |
| ----------- | ------------ | -------- | --------- | ---------------------------------- |
| id          | BIGINT       | 否       |           | 主键，自增                         |
| feedback_code | VARCHAR(16)| 否       |           | 反馈编码，如FB001                  |
| user_id     | BIGINT       | 否       |           | 用户ID，关联users表                |
| title       | VARCHAR(128) | 否       |           | 反馈标题                           |
| content     | TEXT         | 否       |           | 反馈内容                           |
| status      | VARCHAR(16)  | 否       | 'pending' | 状态(pending,processing,completed) |
| reply       | TEXT         | 是       | NULL      | 回复内容                           |
| reply_time  | TIMESTAMP    | 是       | NULL      | 回复时间                           |
| create_time | TIMESTAMP    | 否       | NOW()     | 创建时间                           |
| update_time | TIMESTAMP    | 否       | NOW()     | 更新时间                           |
| is_deleted  | TINYINT      | 否       | 0         | 是否删除，0-否，1-是               |

索引：

- PRIMARY KEY (`id`)
- UNIQUE KEY `idx_feedback_code` (`feedback_code`)
- INDEX `idx_user_id` (`user_id`)
- INDEX `idx_status` (`status`)
- INDEX `idx_create_time` (`create_time`)

## 系统管理模块

### admins 表

存储系统管理员信息。

| 字段名          | 类型         | 允许为空 | 默认值   | 说明                  |
| --------------- | ------------ | -------- | -------- | --------------------- |
| id              | BIGINT       | 否       |          | 主键，自增            |
| username        | VARCHAR(64)  | 否       |          | 管理员账号            |
| nickname        | VARCHAR(64)  | 否       |          | 管理员昵称            |
| password_hash   | VARCHAR(255) | 否       |          | 密码哈希值            |
| password_salt   | VARCHAR(64)  | 否       |          | 密码盐值              |
| status          | VARCHAR(16)  | 否       | 'active' | 状态(active,inactive) |
| remark          | VARCHAR(255) | 是       | NULL     | 备注                  |
| last_login_time | TIMESTAMP    | 是       | NULL     | 最后登录时间          |
| create_time     | TIMESTAMP    | 否       | NOW()    | 创建时间              |
| update_time     | TIMESTAMP    | 否       | NOW()    | 更新时间              |
| is_deleted      | TINYINT      | 否       | 0        | 是否删除，0-否，1-是  |

索引：

- PRIMARY KEY (`id`)
- UNIQUE KEY `idx_username` (`username`)
- INDEX `idx_status` (`status`)

### admin_roles 表

存储管理员与角色的关联关系。

| 字段名      | 类型      | 允许为空 | 默认值 | 说明                   |
| ----------- | --------- | -------- | ------ | ---------------------- |
| id          | BIGINT    | 否       |        | 主键，自增             |
| admin_id    | BIGINT    | 否       |        | 管理员ID，关联admins表 |
| role_id     | BIGINT    | 否       |        | 角色ID，关联roles表    |
| create_time | TIMESTAMP | 否       | NOW()  | 创建时间               |
| update_time | TIMESTAMP | 否       | NOW()  | 更新时间               |
| is_deleted  | TINYINT   | 否       | 0      | 是否删除，0-否，1-是   |

索引：

- PRIMARY KEY (`id`)
- UNIQUE KEY `idx_admin_role` (`admin_id`, `role_id`)
- INDEX `idx_role_id` (`role_id`)

### roles 表

存储系统角色信息。

| 字段名      | 类型         | 允许为空 | 默认值 | 说明                   |
| ----------- | ------------ | -------- | ------ | ---------------------- |
| id          | BIGINT       | 否       |        | 主键，自增             |
| name        | VARCHAR(64)  | 否       |        | 角色名称               |
| description | VARCHAR(255) | 是       | NULL   | 角色描述               |
| parent_id   | BIGINT       | 是       | NULL   | 父角色ID，实现角色继承 |
| sort_order  | INT          | 否       | 0      | 排序顺序               |
| status      | VARCHAR(16)  | 否       | '启用' | 状态(启用,禁用)        |
| create_time | TIMESTAMP    | 否       | NOW()  | 创建时间               |
| update_time | TIMESTAMP    | 否       | NOW()  | 更新时间               |
| is_deleted  | TINYINT      | 否       | 0      | 是否删除，0-否，1-是   |

索引：

- PRIMARY KEY (`id`)
- UNIQUE KEY `idx_name` (`name`)
- INDEX `idx_parent_id` (`parent_id`)
- INDEX `idx_status` (`status`)

### permissions 表

存储系统权限信息。

| 字段名      | 类型         | 允许为空 | 默认值 | 说明                 |
| ----------- | ------------ | -------- | ------ | -------------------- |
| id          | BIGINT       | 否       |        | 主键，自增           |
| name        | VARCHAR(64)  | 否       |        | 权限名称             |
| code        | VARCHAR(64)  | 否       |        | 权限编码             |
| description | VARCHAR(255) | 是       | NULL   | 权限描述             |
| type        | VARCHAR(16)  | 否       |        | 权限类型(菜单,API等) |
| parent_id   | BIGINT       | 是       | NULL   | 父权限ID             |
| path        | VARCHAR(255) | 是       | NULL   | 权限路径             |
| sort_order  | INT          | 否       | 0      | 排序顺序             |
| create_time | TIMESTAMP    | 否       | NOW()  | 创建时间             |
| update_time | TIMESTAMP    | 否       | NOW()  | 更新时间             |
| is_deleted  | TINYINT      | 否       | 0      | 是否删除，0-否，1-是 |

索引：

- PRIMARY KEY (`id`)
- UNIQUE KEY `idx_code` (`code`)
- INDEX `idx_parent_id` (`parent_id`)
- INDEX `idx_type` (`type`)

### role_permissions 表

存储角色与权限的关联关系。

| 字段名        | 类型      | 允许为空 | 默认值 | 说明                      |
| ------------- | --------- | -------- | ------ | ------------------------- |
| id            | BIGINT    | 否       |        | 主键，自增                |
| role_id       | BIGINT    | 否       |        | 角色ID，关联roles表       |
| permission_id | BIGINT    | 否       |        | 权限ID，关联permissions表 |
| create_time   | TIMESTAMP | 否       | NOW()  | 创建时间                  |
| update_time   | TIMESTAMP | 否       | NOW()  | 更新时间                  |
| is_deleted    | TINYINT   | 否       | 0      | 是否删除，0-否，1-是      |

索引：

- PRIMARY KEY (`id`)
- UNIQUE KEY `idx_role_permission` (`role_id`, `permission_id`)
- INDEX `idx_permission_id` (`permission_id`)

### admin_login_logs 表

存储管理员登录日志。

| 字段名      | 类型         | 允许为空 | 默认值 | 说明                   |
| ----------- | ------------ | -------- | ------ | ---------------------- |
| id          | BIGINT       | 否       |        | 主键，自增             |
| admin_id    | BIGINT       | 否       |        | 管理员ID，关联admins表 |
| ip          | VARCHAR(64)  | 否       |        | 登录IP                 |
| user_agent  | VARCHAR(255) | 是       | NULL   | 用户代理               |
| login_time  | TIMESTAMP    | 否       | NOW()  | 登录时间               |
| status      | VARCHAR(16)  | 否       |        | 登录状态(成功,失败)    |
| remark      | VARCHAR(255) | 是       | NULL   | 备注                   |
| create_time | TIMESTAMP    | 否       | NOW()  | 创建时间               |
| is_deleted  | TINYINT      | 否       | 0      | 是否删除，0-否，1-是   |

索引：

- PRIMARY KEY (`id`)
- INDEX `idx_admin_id` (`admin_id`)
- INDEX `idx_login_time` (`login_time`)
- INDEX `idx_status` (`status`)

## 授权管理模块

### auth_codes 表

存储系统授权码信息。

| 字段名      | 类型         | 允许为空 | 默认值   | 说明                 |
| ----------- | ------------ | -------- | -------- | -------------------- |
| id          | BIGINT       | 否       |          | 主键，自增           |
| auth_code   | VARCHAR(128) | 否       |          | 授权码(唯一)         |
| admin_id    | BIGINT       | 否       |          | 所属管理员ID         |
| status      | VARCHAR(16)  | 否       | '未使用' | 状态(未使用,已使用)  |
| user_id     | BIGINT       | 是       | NULL     | 使用该授权码的用户ID |
| used_time   | TIMESTAMP    | 是       | NULL     | 使用时间             |
| create_time | TIMESTAMP    | 否       | NOW()    | 创建时间             |
| update_time | TIMESTAMP    | 否       | NOW()    | 更新时间             |
| is_deleted  | TINYINT      | 否       | 0        | 是否删除，0-否，1-是 |

索引：

- PRIMARY KEY (`id`)
- UNIQUE KEY `idx_auth_code` (`auth_code`)
- INDEX `idx_admin_id` (`admin_id`)
- INDEX `idx_user_id` (`user_id`)
- INDEX `idx_status` (`status`)
- INDEX `idx_create_time` (`create_time`)

### auth_code_logs 表

存储授权码操作日志。

| 字段名       | 类型         | 允许为空 | 默认值 | 说明                     |
| ------------ | ------------ | -------- | ------ | ------------------------ |
| id           | BIGINT       | 否       |        | 主键，自增               |
| auth_code_id | BIGINT       | 否       |        | 授权码ID                 |
| action       | VARCHAR(32)  | 否       |        | 操作类型(创建,使用,废弃) |
| admin_id     | BIGINT       | 是       | NULL   | 操作管理员ID             |
| user_id      | BIGINT       | 是       | NULL   | 操作用户ID               |
| ip           | VARCHAR(64)  | 是       | NULL   | 操作IP                   |
| remark       | VARCHAR(255) | 是       | NULL   | 备注                     |
| create_time  | TIMESTAMP    | 否       | NOW()  | 创建时间                 |
| is_deleted   | TINYINT      | 否       | 0      | 是否删除，0-否，1-是     |

索引：

- PRIMARY KEY (`id`)
- INDEX `idx_auth_code_id` (`auth_code_id`)
- INDEX `idx_admin_id` (`admin_id`)
- INDEX `idx_user_id` (`user_id`)
- INDEX `idx_action` (`action`)
- INDEX `idx_create_time` (`create_time`)

## 表关系图

```
用户模块
  users <-- user_payment_methods (1:1)
      |  <-- auth_codes (1:1)
      |
      v
系统管理模块
  admins <-- admin_roles (1:N) --> roles (N:1)
      |                             |
      |                             v
      |                           permissions (N:M, 通过role_permissions关联)
      |
      v
授权管理模块
  auth_codes <-- auth_code_logs (1:N)
      |
      v
产品模块
  products <-- product_images (1:N)
      ^
      |
      |
      v
计划模块
  plans <-- user_plans (1:N)
      |
  user_plans <-- user_plan_vip_prices (1:N)
      |
      v
钱包模块
  wallets <-- wallet_transactions (1:N)
      |   <-- withdrawals (1:N)
      |
      v
代运营模块
  agency_follows <-- agency_follow_performance (1:1)
      ^
      |
      |
      v
通知模块
  notifications
      ^
      |
      |
      v
推广模块
  coupons <-- user_coupons (1:N)
      ^
      |
      |
      v
反馈模块
  feedbacks
```

## 数据模型与业务关系

1. **用户与钱包**：每个用户拥有一个钱包，钱包中包含交易记录、提现方式等。
2. **用户与管理员**：普通用户关联到特定管理员，管理员可以管理多个用户。
3. **用户与授权码**：用户通过授权码进行系统访问授权，授权码由管理员创建和管理。
4. **管理员与角色**：管理员可以拥有多个角色，每个角色具有不同的权限范围，通过admin_roles表实现多对多关联。
5. **角色与权限**：角色与权限是多对多关系，通过role_permissions表关联。
6. **用户与计划**：系统中存在多个广告计划，当用户参与计划时创建用户计划记录，设置投放金额范围和VIP等级价格。
7. **用户与代运营**：用户可以成为代运营者(通过agency_id)，也可以跟随其他代运营者(通过agency_follows)。
8. **计划与产品**：每个广告计划关联到特定产品，包含产品基本信息、投放内容、规则和受众定向。
9. **派单操作**：管理员可以将计划派发给用户，设置投放金额范围，生成用户计划记录。
10. **优惠券使用**：系统创建优惠券，用户领取后形成用户优惠券记录，可以在计划投放时使用。
11. **通知与反馈**：系统向用户推送通知，用户可以提交反馈意见。

## 事务与一致性建议

在实现过程中，下列操作需要考虑事务处理，确保数据一致性：

1. **计划投放**：涉及plan_orders创建、wallets余额变更、wallet_transactions记录等多表操作。
2. **提现申请**：涉及withdrawals创建、wallets余额冻结、wallet_transactions记录等多表操作。
3. **优惠券使用**：涉及user_coupons状态变更和相关订单记录。
4. **钱包余额更新**：任何影响钱包余额的操作都需要使用事务确保数据一致性。
5. **计划派单操作**：创建user_plans记录和相关VIP价格设置等操作应在一个事务中完成，确保数据一致性。

## 索引优化建议

根据业务特点，除了已设置的索引外，还应考虑：

1. 对经常按时间范围查询的字段添加索引（如交易记录、通知、计划等）。
2. 对经常统计分析的字段添加组合索引（如按用户ID和状态查询计划）。
3. 对金额字段考虑添加索引，以支持范围查询和排序需求。

## 扩展性考虑

1. 考虑使用分区表存储大量增长的历史数据（如交易记录、通知等）。
2. 对于可能需要水平扩展的功能，设计时应考虑分表策略（如按用户ID哈希分表）。
3. 敏感数据（如银行卡号、支付密码等）应使用加密存储，并考虑使用单独的服务管理。

```

```
