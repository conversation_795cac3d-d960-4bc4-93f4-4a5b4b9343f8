# Mock登录状态检查报告

## 📋 检查结果总览

| 检查项目 | 状态 | 说明 |
|---------|------|------|
| Mock服务 | ✅ 完整保留 | backend-mock目录完整存在 |
| Mock用户数据 | ✅ 可用 | vben/admin/jack等测试账号正常 |
| 环境配置 | ✅ 支持切换 | 可通过VITE_NITRO_MOCK控制 |
| Vite代理 | ✅ 智能切换 | 根据环境变量自动选择代理目标 |
| web-admin登录界面 | ❌ 已移除 | Mock用户选择功能已删除 |
| 其他应用登录界面 | ✅ 保留 | web-antd等仍有Mock选择功能 |

## 🔧 Mock服务状态详情

### 1. Mock服务完整性
- **位置**: `apps/backend-mock/`
- **状态**: ✅ 完整保留
- **服务**: Nitro Mock服务器
- **端口**: 5320

### 2. Mock用户数据
```typescript
// 可用的测试账号
const MOCK_USERS = [
  { username: 'vben', password: '123456', realName: 'Vben', roles: ['super'] },
  { username: 'admin', password: '123456', realName: 'Admin', roles: ['admin'] },
  { username: 'jack', password: '123456', realName: 'Jack', roles: ['user'] }
];
```

### 3. Mock API端点
- **登录**: `POST /api/auth/login`
- **用户信息**: `GET /api/user/info`
- **权限码**: `GET /api/auth/codes`
- **菜单**: `GET /api/menu/all`

## 🔄 切换方式说明

### 方式1：环境变量切换（推荐）

#### 切换到Mock模式
```bash
# 修改 apps/web-admin/.env.development
VITE_GLOB_API_URL=/api
VITE_NITRO_MOCK=true
```

#### 切换到真实API模式
```bash
# 修改 apps/web-admin/.env.development
VITE_GLOB_API_URL=/api
VITE_NITRO_MOCK=false
```

### 方式2：创建多个环境文件

#### 创建Mock专用环境文件
```bash
# 创建 apps/web-admin/.env.mock
VITE_GLOB_API_URL=/api
VITE_NITRO_MOCK=true
VITE_PORT=5777
```

#### 创建真实API专用环境文件
```bash
# 创建 apps/web-admin/.env.real
VITE_GLOB_API_URL=/api
VITE_NITRO_MOCK=false
VITE_PORT=5777
```

## 🚀 启动步骤

### Mock模式启动
```bash
# 1. 启动Mock服务器
cd apps/backend-mock
npm run dev

# 2. 修改环境配置
# 设置 VITE_NITRO_MOCK=true

# 3. 启动前端应用
cd apps/web-admin
npm run dev
```

### 真实API模式启动
```bash
# 1. 修改环境配置
# 设置 VITE_NITRO_MOCK=false

# 2. 启动前端应用
cd apps/web-admin
npm run dev
```

## ⚠️ 当前限制和解决方案

### 问题1: web-admin登录界面缺少Mock用户选择
**现状**: web-admin的登录界面已移除Mock用户选择下拉框

**解决方案A**: 恢复Mock用户选择功能
```typescript
// 在 apps/web-admin/src/views/_core/authentication/login.vue 中添加
const MOCK_USER_OPTIONS: BasicOption[] = [
  { label: 'Super', value: 'vben' },
  { label: 'Admin', value: 'admin' },
  { label: 'User', value: 'jack' },
];

// 在formSchema中添加选择组件
{
  component: 'VbenSelect',
  componentProps: {
    options: MOCK_USER_OPTIONS,
    placeholder: $t('authentication.selectAccount'),
  },
  fieldName: 'selectAccount',
  label: $t('authentication.selectAccount'),
}
```

**解决方案B**: 使用其他应用进行Mock测试
- 使用 `apps/web-antd` 进行Mock登录测试
- 该应用仍保留完整的Mock用户选择功能

### 问题2: 需要手动切换环境配置
**解决方案**: 创建环境切换脚本
```bash
# 创建切换脚本 switch-env.sh
#!/bin/bash
if [ "$1" = "mock" ]; then
    echo "VITE_NITRO_MOCK=true" > apps/web-admin/.env.development.local
    echo "切换到Mock模式"
elif [ "$1" = "real" ]; then
    echo "VITE_NITRO_MOCK=false" > apps/web-admin/.env.development.local
    echo "切换到真实API模式"
fi
```

## 🧪 测试验证

### Mock模式测试
```javascript
// 在浏览器控制台验证Mock模式
console.log('Mock模式:', import.meta.env.VITE_NITRO_MOCK);
console.log('API地址:', import.meta.env.VITE_GLOB_API_URL);

// 测试Mock登录
await window.authTest.testLoginConnection()
```

### 真实API模式测试
```javascript
// 验证真实API模式
console.log('Mock模式:', import.meta.env.VITE_NITRO_MOCK);
console.log('代理目标:', 'https://www.fb-hc-ads.click/mng/');

// 测试真实API登录
await window.authTest.runFullLoginTest()
```

## 📝 建议操作

### 立即可行的方案
1. **使用web-antd进行Mock测试**: 该应用保留了完整的Mock功能
2. **环境变量切换**: 通过修改VITE_NITRO_MOCK快速切换模式
3. **保持当前配置**: web-admin专注真实API，其他应用用于Mock测试

### 长期优化方案
1. **恢复web-admin的Mock选择功能**: 添加条件渲染的Mock用户选择
2. **创建环境切换工具**: 开发便捷的切换脚本或界面
3. **统一登录体验**: 在所有应用中保持一致的登录界面

## 🔍 验证命令

```bash
# 检查Mock服务状态
curl http://localhost:5320/api/status

# 检查环境配置
echo $VITE_NITRO_MOCK

# 验证代理配置
# 查看 vite.config.mts 中的代理设置
```

---

**检查时间**: $(date)
**报告版本**: v1.0.0
**建议**: 建议恢复web-admin的Mock用户选择功能以便开发测试
