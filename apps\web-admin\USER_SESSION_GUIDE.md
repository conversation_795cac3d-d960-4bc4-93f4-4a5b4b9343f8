# 用户会话管理指南

## 📋 概述

本文档说明用户会话管理机制，包括登录状态保持、页面刷新处理、会话恢复等功能。

## 🔐 会话状态说明

### 正常情况
1. **首次登录**: 用户输入账号密码 → 获取token → 存储用户信息 → 跳转首页
2. **正常使用**: token和用户信息都存在，可以正常访问所有页面
3. **正常退出**: 点击退出 → 清除token和用户信息 → 跳转登录页

### 异常情况处理
1. **页面刷新**: token存在但用户信息丢失 → 自动创建基础用户信息
2. **直接访问**: 无token无用户信息 → 跳转登录页并保存目标路径
3. **token过期**: 后端返回401 → 自动跳转登录页
4. **异常状态**: 有用户信息但无token → 清除用户信息并跳转登录

## 🔧 会话恢复机制

### 自动恢复
当检测到以下情况时，系统会自动尝试恢复会话：
- 有有效token但用户信息缺失（通常是页面刷新导致）
- 创建基础用户信息，避免频繁跳转登录页
- 保持用户的使用体验

### 基础用户信息
```typescript
{
  userId: '',
  username: 'user',
  realName: 'User', 
  avatar: '',
  roles: [], // 权限由后端token控制
  desc: '基础用户信息（页面刷新后恢复）',
  homePath: '/dashboard',
  token: accessToken,
}
```

## 🚀 使用场景

### 场景1: 页面刷新
**问题**: 用户在使用过程中刷新页面，导致store中的用户信息丢失

**解决**: 
1. 检测到token存在但用户信息缺失
2. 自动创建基础用户信息
3. 用户可以继续使用，无需重新登录

**用户体验**: ✅ 无感知，继续正常使用

### 场景2: 直接访问内部页面
**问题**: 用户直接在地址栏输入内部页面URL

**解决**:
1. 检测到无token无用户信息
2. 跳转到登录页
3. 保存目标路径，登录后自动跳转

**用户体验**: ✅ 登录后直接到达目标页面

### 场景3: Token过期
**问题**: 用户长时间使用，token过期

**解决**:
1. API请求返回401
2. 请求拦截器捕获错误
3. 自动跳转登录页

**用户体验**: ✅ 明确提示需要重新登录

### 场景4: 异常状态
**问题**: 有用户信息但token缺失（异常情况）

**解决**:
1. 检测到异常状态
2. 清除用户信息
3. 跳转登录页

**用户体验**: ✅ 安全处理，避免权限问题

## 🧪 调试工具

### 会话状态检查
```javascript
// 检查当前会话状态
window.sessionRecovery.checkSessionStatus()

// 获取详细诊断信息
window.sessionRecovery.getSessionDiagnostic()
```

### 手动会话操作
```javascript
// 尝试恢复会话
window.sessionRecovery.recoverUserSession()

// 清除会话（测试用）
window.sessionRecovery.clearSession()

// 自动恢复会话
await window.sessionRecovery.autoRecoverSession()
```

### 测试场景
```javascript
// 模拟页面刷新后的状态
localStorage.setItem('accessToken', 'test-token');
window.sessionRecovery.recoverUserSession();

// 模拟token过期
localStorage.removeItem('accessToken');
window.sessionRecovery.checkSessionStatus();
```

## ⚠️ 注意事项

### 安全考虑
1. **基础用户信息**: 只包含必要字段，不包含敏感信息
2. **权限验证**: 完全依赖后端token验证，前端不做权限判断
3. **异常处理**: 发现异常状态时立即清除并要求重新登录

### 用户体验
1. **减少跳转**: 页面刷新时尽量避免跳转到登录页
2. **保存状态**: 跳转登录时保存用户的目标路径
3. **清晰提示**: 异常情况下提供明确的错误信息

### 开发建议
1. **测试刷新**: 开发时经常测试页面刷新的处理
2. **监控日志**: 关注控制台的会话恢复日志
3. **异常处理**: 确保所有异常情况都有合适的处理

## 🔍 故障排除

### 常见问题

1. **刷新后跳转登录页**
   - 检查token是否正确存储
   - 确认会话恢复逻辑是否正常工作
   - 查看控制台日志

2. **登录后无法访问页面**
   - 检查用户信息是否正确设置
   - 确认路由守卫逻辑
   - 验证token格式

3. **频繁要求重新登录**
   - 检查token过期时间
   - 确认后端token验证逻辑
   - 查看网络请求错误

### 调试步骤
1. 打开浏览器开发者工具
2. 查看控制台日志
3. 检查localStorage中的token
4. 使用调试工具检查会话状态
5. 查看Network面板的API请求

## 📈 优化建议

### 短期优化
1. 添加loading状态，提升用户体验
2. 优化错误提示信息
3. 增加会话恢复的成功提示

### 长期优化
1. 实现token自动刷新机制
2. 添加离线状态检测
3. 实现多标签页会话同步
4. 添加会话过期倒计时提示

---

**更新时间**: $(date)
**版本**: v1.0.0
**维护**: 基于token的会话管理
