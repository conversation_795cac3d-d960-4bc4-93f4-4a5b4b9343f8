import { faker } from '@faker-js/faker';
import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, usePageResponseSuccess } from '~/utils/response';

const formatterCN = new Intl.DateTimeFormat('zh-CN', {
  timeZone: 'Asia/Shanghai',
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
});

function generateMockDataList(count: number) {
  const dataList = [];

  for (let i = 0; i < count; i++) {
    const date = faker.date.between({ from: '2024-01-01', to: '2024-12-31' });
    const dataItem: Record<string, any> = {
      id: faker.string.uuid(),
      date: date.toISOString().split('T')[0], // YYYY-MM-DD format
      registrationCount: faker.number.int({ min: 10, max: 500 }),
      updateTime: formatterCN.format(
        faker.date.between({ from: date, to: new Date() }),
      ),
      createTime: formatterCN.format(date),
    };

    dataList.push(dataItem);
  }

  return dataList;
}

const mockData = generateMockDataList(100);

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const {
    page = 1,
    pageSize = 20,
    date,
    registrationCount,
    startTime,
    endTime,
  } = getQuery(event);

  let listData = structuredClone(mockData);

  if (date) {
    listData = listData.filter((item) => item.date === date);
  }

  if (registrationCount) {
    listData = listData.filter(
      (item) => item.registrationCount >= Number(registrationCount),
    );
  }

  if (startTime) {
    listData = listData.filter((item) => item.createTime >= startTime);
  }

  if (endTime) {
    listData = listData.filter((item) => item.createTime <= endTime);
  }

  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
