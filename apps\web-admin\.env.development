# 端口号
VITE_PORT=5777

VITE_BASE=/

# 接口地址
# Mock模式：使用 /api
# 真实后台：使用 /api (通过vite代理到真实后台)

# 使用代理方式访问真实后台API，避免CORS问题
VITE_GLOB_API_URL=/api
VITE_NITRO_MOCK=false

# 是否开启 Nitro Mock服务，true 为开启，false 为关闭
# 开发调试时保持 true，对接真实后台时改为 false

# 真实API配置说明：
# - VITE_GLOB_API_URL=/api 使用本地代理
# - vite.config.mts 中配置代理到 https://www.fb-hc-ads.click/mng/
# - 这样可以避免CORS跨域问题

# 是否打开 devtools，true 为打开，false 为关闭
VITE_DEVTOOLS=false

# 是否注入全局loading
VITE_INJECT_APP_LOADING=true
