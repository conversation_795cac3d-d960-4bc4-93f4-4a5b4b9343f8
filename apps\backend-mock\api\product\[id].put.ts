import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, useResponseSuccess } from '~/utils/response';

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const id = getRouterParam(event, 'id');
  const body = await readBody(event);

  // 模拟更新产品
  const updatedProduct = {
    id,
    ...body,
  };

  // 模拟延迟
  await new Promise((resolve) => setTimeout(resolve, 500));

  return useResponseSuccess(updatedProduct);
});
