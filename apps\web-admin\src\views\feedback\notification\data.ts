import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { FeedbackApi } from '#/api';

import { $t } from '#/locales';

// 获取阅读状态标签
function getReadStatusLabel(status: number): string {
  const labels: Record<number, string> = {
    0: $t('feedback.readStatus.unread'),
    1: $t('feedback.readStatus.read'),
  };
  return labels[status] || status.toString();
}

// 获取通知类型标签
function getNotificationTypeLabel(type: string): string {
  const labels: Record<string, string> = {
    system: $t('feedback.notificationType.system'),
    promotion: $t('feedback.notificationType.promotion'),
    reminder: $t('feedback.notificationType.reminder'),
    warning: $t('feedback.notificationType.warning'),
  };
  return labels[type] || type;
}

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'userId',
      label: $t('feedback.notification.userId'),
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        options: [
          { label: $t('feedback.notificationType.system'), value: 'system' },
          {
            label: $t('feedback.notificationType.promotion'),
            value: 'promotion',
          },
          {
            label: $t('feedback.notificationType.reminder'),
            value: 'reminder',
          },
          { label: $t('feedback.notificationType.warning'), value: 'warning' },
        ],
      },
      fieldName: 'notificationType',
      label: $t('feedback.notification.notificationType'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'notificationTitle',
      label: $t('feedback.notification.notificationTitle'),
      rules: 'required',
    },
    {
      component: 'Textarea',
      fieldName: 'notificationContent',
      label: $t('feedback.notification.notificationContent'),
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        options: [
          { label: $t('feedback.readStatus.unread'), value: 0 },
          { label: $t('feedback.readStatus.read'), value: 1 },
        ],
      },
      defaultValue: 0,
      fieldName: 'readStatus',
      label: $t('feedback.notification.readStatus'),
    },
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择阅读时间',
        style: { width: '100%' },
        showTime: true,
      },
      fieldName: 'readTime',
      label: $t('feedback.notification.readTime'),
    },
  ];
}

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'userId',
      label: $t('feedback.notification.userId'),
    },
    {
      component: 'Select',
      componentProps: {
        options: [
          { label: $t('feedback.notificationType.system'), value: 'system' },
          {
            label: $t('feedback.notificationType.promotion'),
            value: 'promotion',
          },
          {
            label: $t('feedback.notificationType.reminder'),
            value: 'reminder',
          },
          { label: $t('feedback.notificationType.warning'), value: 'warning' },
        ],
      },
      fieldName: 'notificationType',
      label: $t('feedback.notification.notificationType'),
    },
    {
      component: 'Select',
      componentProps: {
        options: [
          { label: $t('feedback.readStatus.unread'), value: 0 },
          { label: $t('feedback.readStatus.read'), value: 1 },
        ],
      },
      fieldName: 'readStatus',
      label: $t('feedback.notification.readStatus'),
    },
  ];
}

export function useColumns<T = FeedbackApi.Notification>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'id',
      title: $t('feedback.notification.id'),
      width: 80,
    },
    {
      field: 'userId',
      title: $t('feedback.notification.userId'),
      width: 120,
    },
    {
      field: 'notificationType',
      title: $t('feedback.notification.notificationType'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: string }) =>
            getNotificationTypeLabel(cellValue),
        },
      },
    },
    {
      field: 'notificationTitle',
      title: $t('feedback.notification.notificationTitle'),
      width: 200,
    },
    {
      field: 'notificationContent',
      title: $t('feedback.notification.notificationContent'),
      minWidth: 250,
      showOverflow: true,
    },
    {
      cellRender: {
        name: 'CellTag',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) =>
            getReadStatusLabel(cellValue),
        },
      },
      field: 'readStatus',
      title: $t('feedback.notification.readStatus'),
      width: 100,
    },
    {
      field: 'readTime',
      title: $t('feedback.notification.readTime'),
      width: 180,
    },
    {
      field: 'updateTime',
      title: $t('feedback.notification.updateTime'),
      width: 180,
    },
    {
      field: 'createTime',
      title: $t('feedback.notification.createTime'),
      width: 180,
    },
    {
      cellRender: {
        name: 'CellActions',
        props: {
          actions: [
            { code: 'view', text: '查看' },
            { code: 'edit', text: '编辑' },
          ],
          onActionClick,
        },
      },
      field: 'action',
      fixed: 'right',
      title: $t('feedback.notification.operation'),
      width: 120,
    },
  ];
}
