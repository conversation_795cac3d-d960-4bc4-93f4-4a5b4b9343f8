/**
 * 通用API服务基类
 * 提供标准的CRUD操作方法
 */

import type { PageFetchParams } from './request';

import { requestClient } from './request';
import { transformPageParams, transformPageResponse } from './adapter';

export class BaseApiService {
  protected baseUrl: string;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  /**
   * 获取列表数据（分页）
   */
  async getList<T = any>(params: PageFetchParams = {}) {
    const transformedParams = transformPageParams(params);
    const response = await requestClient.get(`${this.baseUrl}/list`, {
      params: transformedParams,
    });
    return transformPageResponse(response);
  }

  /**
   * 获取详情
   */
  async getDetail<T = any>(id: string | number) {
    return requestClient.get<T>(`${this.baseUrl}/${id}`);
  }

  /**
   * 创建
   */
  async create<T = any>(data: any) {
    return requestClient.post<T>(this.baseUrl, data);
  }

  /**
   * 更新
   */
  async update<T = any>(id: string | number, data: any) {
    return requestClient.put<T>(`${this.baseUrl}/${id}`, data);
  }

  /**
   * 删除
   */
  async delete(id: string | number) {
    return requestClient.delete(`${this.baseUrl}/${id}`);
  }

  /**
   * 批量删除
   */
  async batchDelete(ids: (string | number)[]) {
    return requestClient.delete(`${this.baseUrl}/batch`, {
      data: { ids },
    });
  }

  /**
   * 更新状态
   */
  async updateStatus(id: string | number, status: any) {
    return requestClient.patch(`${this.baseUrl}/${id}/status`, { status });
  }

  /**
   * 导出数据
   */
  async export(params: any = {}) {
    return requestClient.get(`${this.baseUrl}/export`, {
      params,
      responseType: 'blob',
    });
  }

  /**
   * 导入数据
   */
  async import(file: File) {
    const formData = new FormData();
    formData.append('file', file);
    return requestClient.post(`${this.baseUrl}/import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }
}
