import type { Recordable, UserInfo } from '@vben/types';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { LOGIN_PATH } from '@vben/constants';
import { preferences } from '@vben/preferences';
import { resetAllStores, useAccessStore, useUserStore } from '@vben/stores';

import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';

import { getAccessCodesApi, getUserInfoApi, loginApi, logoutApi } from '#/api';
import { $t } from '#/locales';

export const useAuthStore = defineStore('auth', () => {
  const accessStore = useAccessStore();
  const userStore = useUserStore();
  const router = useRouter();

  const loginLoading = ref(false);

  /**
   * 异步处理登录操作
   * Asynchronously handle the login process
   * @param params 登录表单数据
   * @param onSuccess 成功之后的回调函数
   */
  async function authLogin(
    params: Recordable<any>,
    onSuccess?: () => Promise<void> | void,
  ) {
    // 异步处理用户登录操作并获取 accessToken
    let userInfo: null | UserInfo = null;
    try {
      loginLoading.value = true;

      // 调用真实登录API
      const loginParams = {
        username: params.username,
        password: params.password,
      };
      const loginResult = await loginApi(loginParams);
      const { accessToken, userInfo: loginUserInfo, permissions } = loginResult;

      // 如果成功获取到 accessToken
      if (accessToken) {
        // 存储访问令牌
        accessStore.setAccessToken(accessToken);

        // 如果登录接口返回了用户信息，直接使用；否则重新获取
        if (loginUserInfo) {
          // 将登录返回的用户信息转换为标准格式
          userInfo = {
            userId: loginUserInfo.id?.toString() || '',
            username: loginUserInfo.username || '',
            realName: loginUserInfo.nickname || loginUserInfo.username || '',
            avatar: '',
            roles: [], // 角色信息可能需要从权限中推导
            desc: loginUserInfo.status || '',
            homePath: '/dashboard',
            token: accessToken,
          } as UserInfo;

          userStore.setUserInfo(userInfo);
        } else {
          // 如果登录接口没有返回用户信息，则单独获取
          userInfo = await fetchUserInfo();
        }

        // 设置权限码
        if (permissions && permissions.length > 0) {
          accessStore.setAccessCodes(permissions);
        } else {
          // 如果登录接口没有返回权限，尝试单独获取
          try {
            const accessCodes = await getAccessCodesApi();
            accessStore.setAccessCodes(accessCodes);
          } catch (error) {
            console.warn('获取权限码失败，使用默认权限:', error);
            accessStore.setAccessCodes([]);
          }
        }

        if (accessStore.loginExpired) {
          accessStore.setLoginExpired(false);
        } else {
          onSuccess
            ? await onSuccess?.()
            : await router.push(
                userInfo.homePath || preferences.app.defaultHomePath,
              );
        }

        if (userInfo?.realName) {
          notification.success({
            description: `${$t('authentication.loginSuccessDesc')}:${userInfo?.realName}`,
            duration: 3,
            message: $t('authentication.loginSuccess'),
          });
        }
      }
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    } finally {
      loginLoading.value = false;
    }

    return {
      userInfo,
    };
  }

  async function logout(redirect: boolean = true) {
    try {
      await logoutApi();
    } catch {
      // 不做任何处理
    }

    resetAllStores();
    accessStore.setLoginExpired(false);

    // 回登录页带上当前路由地址
    await router.replace({
      path: LOGIN_PATH,
      query: redirect
        ? {
            redirect: encodeURIComponent(router.currentRoute.value.fullPath),
          }
        : {},
    });
  }

  async function fetchUserInfo() {
    let userInfo: null | UserInfo = null;
    userInfo = await getUserInfoApi();
    userStore.setUserInfo(userInfo);
    return userInfo;
  }

  function $reset() {
    loginLoading.value = false;
  }

  return {
    $reset,
    authLogin,
    fetchUserInfo,
    loginLoading,
    logout,
  };
});
