import { faker } from '@faker-js/faker';
import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, useResponseSuccess } from '~/utils/response';

const formatterCN = new Intl.DateTimeFormat('zh-CN', {
  timeZone: 'Asia/Shanghai',
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
});

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const body = await readBody(event);

  // 模拟创建用户计划
  const currentTime = formatterCN.format(new Date());
  const newUserPlan = {
    id: faker.string.uuid(),
    ...body,
    createTime: currentTime,
    updateTime: currentTime,
  };

  // 模拟延迟
  await new Promise((resolve) => setTimeout(resolve, 500));

  return useResponseSuccess(newUserPlan);
});
