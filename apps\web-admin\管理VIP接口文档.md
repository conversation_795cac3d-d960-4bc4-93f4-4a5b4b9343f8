# VIP等级管理接口文档

## 目录
1. [获取VIP等级列表](#1-获取vip等级列表)
2. [获取VIP等级详情](#2-获取vip等级详情)
3. [创建VIP等级](#3-创建vip等级)
4. [更新VIP等级](#4-更新vip等级)
5. [删除VIP等级](#5-删除vip等级)

## 接口说明

### 1. 获取VIP等级列表

#### 接口描述
获取系统中所有VIP等级的列表，支持分页和筛选。

#### 请求方式
- **HTTP请求方法**: GET
- **请求URL**: `/mng/vips`

#### 请求参数
| 参数名 | 类型 | 是否必须 | 描述 |
| ----- | ---- | ------- | ---- |
| page | integer | 否 | 页码，默认为1 |
| page_size | integer | 否 | 每页记录数，默认为20 |
| name | string | 否 | 按VIP名称搜索 |
| level | integer | 否 | 按VIP等级筛选 |

#### 响应参数
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "total": 5,
        "page": 1,
        "page_size": 20,
        "list": [
            {
                "id": 1,
                "name": "青铜会员",
                "description": "入门级VIP",
                "icon": "https://example.com/vip/bronze.png",
                "level": 1,
                "commission_ratios": {"level1": 0.05, "level2": 0.03},
                "price": 99.00,
                "subordinate_limit": 10,
                "level_ratios": {"level1": 0.8, "level2": 0.9},
                "create_time": "2025-05-24 12:30:00",
                "update_time": "2025-05-24 12:30:00"
            }
        ]
    }
}
```

### 2. 获取VIP等级详情

#### 接口描述
获取指定VIP等级的详细信息。

#### 请求方式
- **HTTP请求方法**: GET
- **请求URL**: `/mng/vips/{id}`

#### 请求参数
| 参数名 | 类型 | 是否必须 | 描述 |
| ----- | ---- | ------- | ---- |
| id | integer | 是 | VIP等级ID |

#### 响应参数
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "id": 1,
        "name": "青铜会员",
        "description": "入门级VIP",
        "icon": "https://example.com/vip/bronze.png",
        "level": 1,
        "commission_ratios": {"level1": 0.05, "level2": 0.03},
        "price": 99.00,
        "subordinate_limit": 10,
        "level_ratios": {"level1": 0.8, "level2": 0.9},
        "user_count": 120,
        "create_time": "2025-05-24 12:30:00",
        "update_time": "2025-05-24 12:30:00"
    }
}
```

### 3. 创建VIP等级

#### 接口描述
创建新的VIP等级。

#### 请求方式
- **HTTP请求方法**: POST
- **请求URL**: `/mng/vips`

#### 请求参数
| 参数名 | 类型 | 是否必须 | 描述 |
| ----- | ---- | ------- | ---- |
| name | string | 是 | VIP名称，最大长度64字符 |
| description | string | 否 | VIP描述 |
| icon | string | 否 | VIP图标URL，最大长度255字符 |
| level | integer | 是 | VIP等级，必须唯一 |
| commission_ratios | json | 是 | 佣金比例，JSON格式 |
| price | decimal | 是 | 购买VIP价格 |
| subordinate_limit | integer | 是 | 下级数量限额 |
| level_ratios | json | 是 | 当前等级VIP点击价格，JSON格式 |

#### 请求示例
```json
{
    "name": "白银会员",
    "description": "中级VIP",
    "icon": "https://example.com/vip/silver.png",
    "level": 2,
    "commission_ratios": {"level1": 0.08, "level2": 0.05},
    "price": 199.00,
    "subordinate_limit": 20,
    "level_ratios": {"level1": 0.7, "level2": 0.8}
}
```

#### 响应参数
```json
{
    "code": 0,
    "message": "创建成功",
    "data": {
        "id": 2
    }
}
```

### 4. 更新VIP等级

#### 接口描述
更新指定VIP等级的信息。

#### 请求方式
- **HTTP请求方法**: PUT
- **请求URL**: `/mng/vips/{id}`

#### 请求参数
| 参数名 | 类型 | 是否必须 | 描述 |
| ----- | ---- | ------- | ---- |
| id | integer | 是 | VIP等级ID（URL参数） |
| name | string | 否 | VIP名称，最大长度64字符 |
| description | string | 否 | VIP描述 |
| icon | string | 否 | VIP图标URL，最大长度255字符 |
| level | integer | 否 | VIP等级，必须唯一 |
| commission_ratios | json | 否 | 佣金比例，JSON格式 |
| price | decimal | 否 | 购买VIP价格 |
| subordinate_limit | integer | 否 | 下级数量限额 |
| level_ratios | json | 否 | 当前等级VIP点击价格，JSON格式 |

#### 请求示例
```json
{
    "name": "白银会员Plus",
    "price": 249.00,
    "subordinate_limit": 25
}
```

#### 响应参数
```json
{
    "code": 0,
    "message": "更新成功",
    "data": null
}
```

### 5. 删除VIP等级

#### 接口描述
删除指定VIP等级。注意：如果有用户正在使用该VIP等级，则无法删除。

#### 请求方式
- **HTTP请求方法**: DELETE
- **请求URL**: `/mng/vips/{id}`

#### 请求参数
| 参数名 | 类型 | 是否必须 | 描述 |
| ----- | ---- | ------- | ---- |
| id | integer | 是 | VIP等级ID（URL参数） |

#### 响应参数
```json
{
    "code": 0,
    "message": "删除成功",
    "data": null
}
```

#### 错误响应
```json
{
    "code": 1004,
    "message": "当前有 120 位用户正在使用该VIP等级，无法删除",
    "data": null
}
```

## 错误码说明

| 错误码 | 描述 |
| ----- | ---- |
| 0 | 成功 |
| 1001 | 参数验证失败 |
| 1002 | 操作失败 |
| 1003 | 记录不存在 |
| 1004 | 业务逻辑错误 | 
