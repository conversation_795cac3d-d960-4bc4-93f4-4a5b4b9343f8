import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

export namespace FinanceApi {
  export interface Recharge {
    [key: string]: any;
    id: string;
    userId: string;
    userNickname: string;
    fbId?: string;
    floatingAmount: number;
    rechargeType: 'auto' | 'bonus' | 'manual' | 'refund';
    rechargeAmount: number;
    rechargeMethod: 'alipay' | 'bank' | 'crypto' | 'wechat';
    status: 'cancelled' | 'completed' | 'failed' | 'pending' | 'processing';
    virtualCoinRecovery?: number;
    adminNote?: string;
    userNote?: string;
    updateTime?: string;
    createTime?: string;
  }

  export interface Withdrawal {
    [key: string]: any;
    id: string;
    userId: string;
    userNickname: string;
    withdrawalCode: string;
    floatingAmount: number;
    status: 'cancelled' | 'completed' | 'failed' | 'pending' | 'processing';
    requestTime?: string;
    completeTime?: string;
  }

  export interface Transaction {
    [key: string]: any;
    id: string;
    userId: string;
    userName: string;
    transactionCode: string;
    transactionAmount: number;
    transactionType:
      | 'bonus'
      | 'purchase'
      | 'recharge'
      | 'refund'
      | 'transfer'
      | 'withdrawal';
    flowDirection: 'in' | 'out';
    balanceBefore: number;
    balanceAfter: number;
    relatedId?: string;
    status: 'cancelled' | 'completed' | 'failed' | 'pending' | 'processing';
    remark?: string;
    createTime?: string;
  }
}

// 充值管理相关API
async function getRechargeList(params: Recordable<any>) {
  return requestClient.get<Array<FinanceApi.Recharge>>(
    '/finance/recharge/list',
    { params },
  );
}

async function createRecharge(
  data: Omit<FinanceApi.Recharge, 'createTime' | 'id' | 'updateTime'>,
) {
  return requestClient.post('/finance/recharge', data);
}

async function updateRecharge(
  id: string,
  data: Omit<FinanceApi.Recharge, 'createTime' | 'id' | 'updateTime'>,
) {
  return requestClient.put(`/finance/recharge/${id}`, data);
}

async function deleteRecharge(id: string) {
  return requestClient.delete(`/finance/recharge/${id}`);
}

// 提现管理相关API
async function getWithdrawalList(params: Recordable<any>) {
  return requestClient.get<Array<FinanceApi.Withdrawal>>(
    '/finance/withdrawal/list',
    { params },
  );
}

async function createWithdrawal(
  data: Omit<FinanceApi.Withdrawal, 'completeTime' | 'id' | 'requestTime'>,
) {
  return requestClient.post('/finance/withdrawal', data);
}

async function updateWithdrawal(
  id: string,
  data: Omit<FinanceApi.Withdrawal, 'completeTime' | 'id' | 'requestTime'>,
) {
  return requestClient.put(`/finance/withdrawal/${id}`, data);
}

async function deleteWithdrawal(id: string) {
  return requestClient.delete(`/finance/withdrawal/${id}`);
}

// 交易记录相关API
async function getTransactionList(params: Recordable<any>) {
  return requestClient.get<Array<FinanceApi.Transaction>>(
    '/finance/transaction/list',
    { params },
  );
}

async function createTransaction(
  data: Omit<FinanceApi.Transaction, 'createTime' | 'id'>,
) {
  return requestClient.post('/finance/transaction', data);
}

async function updateTransaction(
  id: string,
  data: Omit<FinanceApi.Transaction, 'createTime' | 'id'>,
) {
  return requestClient.put(`/finance/transaction/${id}`, data);
}

async function deleteTransaction(id: string) {
  return requestClient.delete(`/finance/transaction/${id}`);
}

export {
  createRecharge,
  createTransaction,
  createWithdrawal,
  deleteRecharge,
  deleteTransaction,
  deleteWithdrawal,
  getRechargeList,
  getTransactionList,
  getWithdrawalList,
  updateRecharge,
  updateTransaction,
  updateWithdrawal,
};
