<script lang="ts" setup>
import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { createProduct, updateProduct } from '#/api';
import { $t } from '#/locales';

import { useFormSchema } from '../data';

const emits = defineEmits(['success']);

const formData = ref<any>();
const id = ref();

const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
});

const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;

    const values = await formApi.getValues();

    // 处理上传组件的数据格式
    const submitData = { ...values };
    if (submitData.productLogo) {
      submitData.productLogo = formatSubmitValue(submitData.productLogo);
    }
    if (submitData.productMainImage) {
      submitData.productMainImage = formatSubmitValue(
        submitData.productMainImage,
      );
    }

    // app_info字段已经由AppInfoEditor组件处理，无需额外转换

    console.log('📤 提交的产品数据:', submitData);

    drawerApi.lock();
    try {
      if (id.value) {
        await updateProduct(id.value, submitData);
        message.success(`${$t('product.name')}修改成功`);
      } else {
        await createProduct(submitData);
        message.success(`${$t('product.name')}创建成功`);
      }
      emits('success');
      drawerApi.close();
    } catch (error) {
      console.error('操作失败:', error);
      message.error($t('ui.actionMessage.operationFailed'));
      drawerApi.unlock();
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<any>();
      formApi.resetForm();
      if (data && Object.keys(data).length > 0) {
        formData.value = data;
        id.value = data.id;

        // 处理上传组件的数据格式
        const formValues = { ...data };
        if (formValues.productLogo) {
          formValues.productLogo = formatUploadValue(formValues.productLogo);
        }
        if (formValues.productMainImage) {
          formValues.productMainImage = formatUploadValue(
            formValues.productMainImage,
          );
        }

        // app_info字段已经由AppInfoEditor组件处理，无需额外转换

        console.log('📥 回显的表单数据:', formValues);
        formApi.setValues(formValues);
      } else {
        formData.value = {};
        id.value = undefined;
      }
    }
  },
});

// 处理上传组件提交的数据格式
function formatSubmitValue(fileList: any[]) {
  if (!fileList || !Array.isArray(fileList) || fileList.length === 0) {
    return '';
  }
  const file = fileList[0];
  return file.response?.url || file.url || '';
}

// 处理上传组件的数据格式
function formatUploadValue(url: string) {
  if (!url) return [];
  return [
    {
      uid: '-1',
      name: 'image.png',
      status: 'done',
      url,
    },
  ];
}

const getDrawerTitle = computed(() => {
  return formData.value?.id
    ? `修改${$t('product.name')}`
    : `新增${$t('product.name')}`;
});
</script>

<template>
  <Drawer :title="getDrawerTitle">
    <Form />
  </Drawer>
</template>
