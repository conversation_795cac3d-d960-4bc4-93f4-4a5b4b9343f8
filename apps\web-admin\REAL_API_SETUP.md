# 真实API登录配置说明

## 📋 配置概述

本项目已成功从Mock登录模式切换到真实API登录模式。以下是详细的配置说明和使用指南。

## 🔧 环境配置

### 当前配置
- **API地址**: `https://www.fb-hc-ads.click/mng/`
- **Mock模式**: 已禁用 (`VITE_NITRO_MOCK=false`)
- **认证方式**: Bearer Token

### 环境变量文件 (.env.development)
```bash
# 使用代理方式访问真实后台API，避免CORS问题
VITE_GLOB_API_URL=/api
# 禁用Mock服务
VITE_NITRO_MOCK=false
```

### Vite代理配置 (vite.config.mts)
```typescript
// 真实API代理配置
'/api': {
  changeOrigin: true,
  rewrite: (path) => path.replace(/^\/api/, ''),
  target: 'https://www.fb-hc-ads.click/mng/',
  secure: true,
  // 自动处理CORS
}
```

## 🔐 登录流程

### 1. 登录接口
- **地址**: `POST /auth/login`
- **参数**: 
  ```typescript
  {
    username: string;
    password: string;
  }
  ```
- **返回格式**:
  ```typescript
  {
    token: string;
    admin: {
      id: number;
      username: string;
      nickname: string;
      status: string;
      last_login_time: string;
    };
    permissions: string[];
  }
  ```

### 2. 用户信息接口
- **地址**: `GET /auth/profile`
- **认证**: 需要Bearer Token
- **返回格式**: 管理员详细信息

### 3. 权限码接口
- **地址**: `GET /auth/codes`
- **认证**: 需要Bearer Token
- **返回格式**: `string[]`

## 🛠️ 技术实现

### Token管理
1. **存储**: 登录成功后自动存储到 `accessStore`
2. **注入**: 请求拦截器自动在请求头中添加 `Authorization: Bearer {token}`
3. **刷新**: 支持自动token刷新机制
4. **过期处理**: 自动检测token过期并重新认证

### 请求拦截器
- 自动添加Authorization头
- 自动处理token过期
- 统一错误处理
- 支持国际化语言头

### 响应处理
- 统一数据格式处理
- 错误信息提取和显示
- 大数字处理 (JSONBigInt)

## 🧪 测试工具

### 开发环境测试
在浏览器控制台中可以使用以下测试命令：

```javascript
// 测试登录API连接
await window.authTest.testLoginConnection()

// 测试用户信息API
await window.authTest.testUserInfoApi()

// 测试权限码API
await window.authTest.testAccessCodesApi()

// 运行完整测试
await window.authTest.runFullLoginTest()
```

### 测试结果说明
- ✅ **成功**: API连接正常，返回预期数据
- ❌ **失败**: 显示详细错误信息，包括状态码和错误消息

## 🔄 登录界面变更

### 移除的功能
- Mock用户选择下拉框
- 自动填充测试账号密码
- Mock用户依赖逻辑

### 保留的功能
- 用户名密码输入
- 滑动验证码
- 登录状态管理
- 错误处理和提示

## 🚨 故障排除

### 常见问题

1. **CORS错误**
   - ✅ **已解决**: 使用Vite代理避免跨域问题
   - 开发环境: `/api` → `https://www.fb-hc-ads.click/mng/`
   - 生产环境: 需要后端配置CORS或使用Nginx代理

2. **401未授权**
   - 检查用户名密码是否正确
   - 确认API地址是否正确

3. **网络连接错误**
   - 检查API地址是否可访问
   - 确认网络连接状态

4. **Token格式错误**
   - 确认后端返回的token格式
   - 检查Bearer Token前缀

### 调试步骤

1. **检查网络请求**
   - 打开浏览器开发者工具
   - 查看Network面板中的API请求
   - 检查请求头和响应数据

2. **查看控制台日志**
   - 检查是否有JavaScript错误
   - 查看API调用的详细日志

3. **使用测试工具**
   - 运行 `window.authTest.runFullLoginTest()`
   - 查看详细的测试结果

## 📝 后续开发

### API适配
如果后端接口格式发生变化，需要修改以下文件：
- `src/api/core/auth.ts` - 登录和认证相关API
- `src/api/core/user.ts` - 用户信息API
- `src/store/auth.ts` - 认证状态管理

### 扩展功能
- 记住登录状态
- 多因素认证
- 单点登录 (SSO)
- 密码强度验证

## 🔒 安全注意事项

1. **HTTPS**: 生产环境必须使用HTTPS
2. **Token安全**: 避免在URL或日志中暴露token
3. **密码安全**: 不在前端存储明文密码
4. **会话管理**: 实现适当的会话超时机制

## 📞 技术支持

如果遇到问题，请检查：
1. 环境配置是否正确
2. API地址是否可访问
3. 后端服务是否正常运行
4. 网络连接是否稳定

---

**配置完成时间**: $(date)
**配置版本**: v1.0.0
**维护人员**: AI Assistant
