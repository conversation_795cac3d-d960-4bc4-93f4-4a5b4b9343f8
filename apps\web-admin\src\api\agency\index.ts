import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

export namespace AgencyApi {
  export interface Agency {
    [key: string]: any;
    id: string;
    userId: string;
    userNickname: string;
    agencyId: string;
    followedUserId: string;
    followedAgencyId: string;
    status: 0 | 1;
    updateTime?: string;
    createTime?: string;
  }
}

/**
 * 获取代运营列表数据
 */
async function getAgencyList(params: Recordable<any>) {
  return requestClient.get<Array<AgencyApi.Agency>>('/agency/list', { params });
}

/**
 * 创建代运营
 * @param data 代运营数据
 */
async function createAgency(
  data: Omit<AgencyApi.Agency, 'createTime' | 'id' | 'updateTime'>,
) {
  return requestClient.post('/agency', data);
}

/**
 * 更新代运营
 *
 * @param id 代运营 ID
 * @param data 代运营数据
 */
async function updateAgency(
  id: string,
  data: Omit<AgencyApi.Agency, 'createTime' | 'id' | 'updateTime'>,
) {
  return requestClient.put(`/agency/${id}`, data);
}

/**
 * 删除代运营
 * @param id 代运营 ID
 */
async function deleteAgency(id: string) {
  return requestClient.delete(`/agency/${id}`);
}

export { createAgency, deleteAgency, getAgencyList, updateAgency };
