import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

export namespace UserPlanApi {
  export interface UserPlan {
    [key: string]: any;
    id: string;
    groupPlanId: string;
    userId: string;
    userNickname: string;
    orderNumber: string;
    groupOrder: number;
    launchAmount: number;
    launchProgress: number;
    launched: number;
    pending: number;
    launchStatus: 'completed' | 'failed' | 'launching' | 'paused' | 'pending';
    clickPrice: number;
    updateTime?: string;
    createTime?: string;
  }
}

/**
 * 获取用户计划列表数据
 */
async function getUserPlanList(params: Recordable<any>) {
  return requestClient.get<Array<UserPlanApi.UserPlan>>('/user-plan/list', {
    params,
  });
}

/**
 * 创建用户计划
 * @param data 用户计划数据
 */
async function createUserPlan(
  data: Omit<UserPlanApi.UserPlan, 'createTime' | 'id' | 'updateTime'>,
) {
  return requestClient.post('/user-plan', data);
}

/**
 * 更新用户计划
 *
 * @param id 用户计划 ID
 * @param data 用户计划数据
 */
async function updateUserPlan(
  id: string,
  data: Omit<UserPlanApi.UserPlan, 'createTime' | 'id' | 'updateTime'>,
) {
  return requestClient.put(`/user-plan/${id}`, data);
}

/**
 * 删除用户计划
 * @param id 用户计划 ID
 */
async function deleteUserPlan(id: string) {
  return requestClient.delete(`/user-plan/${id}`);
}

export { createUserPlan, deleteUserPlan, getUserPlanList, updateUserPlan };
