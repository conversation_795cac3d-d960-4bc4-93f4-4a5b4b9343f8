import { defineConfig } from '@vben/vite-config';

export default defineConfig(async () => {
  // 检查是否启用了mock模式
  const isMockEnabled = process.env.VITE_NITRO_MOCK === 'true';

  return {
    application: {},
    vite: {
      server: {
        proxy: isMockEnabled ? {
          // Mock模式代理配置
          '/api': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/api/, ''),
            target: 'http://localhost:5320/api',
            ws: true,
          },
        } : {
          // 真实API代理配置
          '/api': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/api/, ''),
            // 真实后台API地址
            target: 'https://www.fb-hc-ads.click/mng/',
            ws: true,
            secure: true, // 支持HTTPS
            // 添加CORS头
            configure: (proxy, options) => {
              proxy.on('proxyRes', (proxyRes, req, res) => {
                proxyRes.headers['Access-Control-Allow-Origin'] = '*';
                proxyRes.headers['Access-Control-Allow-Methods'] = 'GET,POST,PUT,DELETE,OPTIONS';
                proxyRes.headers['Access-Control-Allow-Headers'] = 'Content-Type,Authorization,Accept-Language';
              });
            },
          },
        },
      },
    },
  };
});
