import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { FinanceApi } from '#/api';

import { $t } from '#/locales';

// 获取充值类型显示名称
function getRechargeTypeLabel(type: string): string {
  const typeMap: Record<string, string> = {
    manual: $t('finance.rechargeTypes.manual'),
    auto: $t('finance.rechargeTypes.auto'),
    bonus: $t('finance.rechargeTypes.bonus'),
    refund: $t('finance.rechargeTypes.refund'),
  };
  return typeMap[type] || type;
}

// 获取充值方式显示名称
function getRechargeMethodLabel(method: string): string {
  const methodMap: Record<string, string> = {
    alipay: $t('finance.rechargeMethods.alipay'),
    wechat: $t('finance.rechargeMethods.wechat'),
    bank: $t('finance.rechargeMethods.bank'),
    crypto: $t('finance.rechargeMethods.crypto'),
  };
  return methodMap[method] || method;
}

// 获取交易类型显示名称
function getTransactionTypeLabel(type: string): string {
  const typeMap: Record<string, string> = {
    recharge: $t('finance.transactionTypes.recharge'),
    withdrawal: $t('finance.transactionTypes.withdrawal'),
    transfer: $t('finance.transactionTypes.transfer'),
    purchase: $t('finance.transactionTypes.purchase'),
    refund: $t('finance.transactionTypes.refund'),
    bonus: $t('finance.transactionTypes.bonus'),
  };
  return typeMap[type] || type;
}

// 获取流动方向显示名称
function getFlowDirectionLabel(direction: string): string {
  const directionMap: Record<string, string> = {
    in: $t('finance.flowDirections.in'),
    out: $t('finance.flowDirections.out'),
  };
  return directionMap[direction] || direction;
}

// 获取状态显示名称
function getStatusLabel(status: string): string {
  const statusMap: Record<string, string> = {
    pending: $t('finance.statusTypes.pending'),
    processing: $t('finance.statusTypes.processing'),
    completed: $t('finance.statusTypes.completed'),
    failed: $t('finance.statusTypes.failed'),
    cancelled: $t('finance.statusTypes.cancelled'),
  };
  return statusMap[status] || status;
}

// 充值管理表格列配置
export function useRechargeColumns<T = FinanceApi.Recharge>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'id',
      title: $t('finance.id'),
      width: 80,
    },
    {
      field: 'userId',
      title: $t('finance.userId'),
      width: 120,
    },
    {
      field: 'userNickname',
      title: $t('finance.userNickname'),
      width: 150,
    },
    {
      field: 'fbId',
      title: $t('finance.fbId'),
      width: 120,
    },
    {
      field: 'floatingAmount',
      title: $t('finance.floatingAmount'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => `¥${cellValue}`,
        },
      },
    },
    {
      field: 'rechargeType',
      title: $t('finance.rechargeType'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: string }) =>
            getRechargeTypeLabel(cellValue),
        },
      },
    },
    {
      field: 'rechargeAmount',
      title: $t('finance.rechargeAmount'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => `¥${cellValue}`,
        },
      },
    },
    {
      field: 'rechargeMethod',
      title: $t('finance.rechargeMethod'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: string }) =>
            getRechargeMethodLabel(cellValue),
        },
      },
    },
    {
      field: 'status',
      title: $t('finance.status'),
      width: 100,
      cellRender: {
        name: 'CellTag',
        attrs: {
          formatter: ({ cellValue }: { cellValue: string }) =>
            getStatusLabel(cellValue),
        },
      },
    },
    {
      field: 'virtualCoinRecovery',
      title: $t('finance.virtualCoinRecovery'),
      width: 140,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) =>
            cellValue ? `¥${cellValue}` : '-',
        },
      },
    },
    {
      field: 'adminNote',
      title: $t('finance.adminNote'),
      minWidth: 150,
      showOverflow: true,
    },
    {
      field: 'userNote',
      title: $t('finance.userNote'),
      minWidth: 150,
      showOverflow: true,
    },
    {
      field: 'updateTime',
      title: $t('finance.updateTime'),
      width: 180,
    },
    {
      field: 'createTime',
      title: $t('finance.createTime'),
      width: 180,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'userNickname',
          nameTitle: $t('finance.name'),
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: $t('finance.operation'),
      width: 130,
    },
  ];
}

// 提现管理表格列配置
export function useWithdrawalColumns<T = FinanceApi.Withdrawal>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'id',
      title: $t('finance.id'),
      width: 80,
    },
    {
      field: 'userId',
      title: $t('finance.userId'),
      width: 120,
    },
    {
      field: 'userNickname',
      title: $t('finance.userNickname'),
      width: 150,
    },
    {
      field: 'withdrawalCode',
      title: $t('finance.withdrawalCode'),
      width: 150,
    },
    {
      field: 'floatingAmount',
      title: $t('finance.floatingAmount'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => `¥${cellValue}`,
        },
      },
    },
    {
      field: 'status',
      title: $t('finance.status'),
      width: 100,
      cellRender: {
        name: 'CellTag',
        attrs: {
          formatter: ({ cellValue }: { cellValue: string }) =>
            getStatusLabel(cellValue),
        },
      },
    },
    {
      field: 'requestTime',
      title: $t('finance.requestTime'),
      width: 180,
    },
    {
      field: 'completeTime',
      title: $t('finance.completeTime'),
      width: 180,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'userNickname',
          nameTitle: $t('finance.name'),
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: $t('finance.operation'),
      width: 130,
    },
  ];
}

// 交易记录表格列配置
export function useTransactionColumns<T = FinanceApi.Transaction>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'id',
      title: $t('finance.id'),
      width: 80,
    },
    {
      field: 'userId',
      title: $t('finance.userId'),
      width: 120,
    },
    {
      field: 'userName',
      title: $t('finance.userName'),
      width: 150,
    },
    {
      field: 'transactionCode',
      title: $t('finance.transactionCode'),
      width: 150,
    },
    {
      field: 'transactionAmount',
      title: $t('finance.transactionAmount'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => `¥${cellValue}`,
        },
      },
    },
    {
      field: 'transactionType',
      title: $t('finance.transactionType'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: string }) =>
            getTransactionTypeLabel(cellValue),
        },
      },
    },
    {
      field: 'flowDirection',
      title: $t('finance.flowDirection'),
      width: 100,
      cellRender: {
        name: 'CellTag',
        attrs: {
          formatter: ({ cellValue }: { cellValue: string }) =>
            getFlowDirectionLabel(cellValue),
        },
      },
    },
    {
      field: 'balanceBefore',
      title: $t('finance.balanceBefore'),
      width: 130,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => `¥${cellValue}`,
        },
      },
    },
    {
      field: 'balanceAfter',
      title: $t('finance.balanceAfter'),
      width: 130,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => `¥${cellValue}`,
        },
      },
    },
    {
      field: 'relatedId',
      title: $t('finance.relatedId'),
      width: 120,
    },
    {
      field: 'status',
      title: $t('finance.status'),
      width: 100,
      cellRender: {
        name: 'CellTag',
        attrs: {
          formatter: ({ cellValue }: { cellValue: string }) =>
            getStatusLabel(cellValue),
        },
      },
    },
    {
      field: 'remark',
      title: $t('finance.remark'),
      minWidth: 150,
      showOverflow: true,
    },
    {
      field: 'createTime',
      title: $t('finance.createTime'),
      width: 180,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'userName',
          nameTitle: $t('finance.name'),
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: $t('finance.operation'),
      width: 130,
    },
  ];
}
