/**
 * 字段映射测试工具
 * 用于验证前后端字段映射的正确性
 */

export interface FieldMappingTestResult {
  success: boolean;
  message: string;
  details?: any;
  error?: any;
}

/**
 * 测试字段映射的正确性
 */
export async function testFieldMapping(): Promise<FieldMappingTestResult> {
  try {
    console.log('🧪 测试字段映射...');
    
    // 模拟后端返回的数据格式
    const backendData = {
      id: 1,
      name: 'Test Product',
      category: 'games',
      company: 'Test Company',
      description: 'Test Description',
      logo: 'https://example.com/logo.png',
      image: 'https://example.com/image.png',
      google_play_link: 'https://play.google.com/store/apps/details?id=test.app',
      app_store_link: 'https://apps.apple.com/us/app/test-app/id123456789',
      status: '生效',
      app_info: {
        'Category': 'Simulation',
        'Languages': 'English, Chinese, French',
        'Age Rating': '12+',
        'iOS Requires': 'iOS 13.0 or higher',
        'Android Requires': '5.0 or higher',
      },
    };
    
    console.log('📥 模拟后端数据:', backendData);
    
    // 导入转换函数
    const productApi = await import('#/api/product');
    
    // 测试后端到前端的转换
    const frontendData = productApi.transformBackendToFrontend?.(backendData);
    console.log('🔄 转换为前端格式:', frontendData);
    
    // 验证关键字段
    const fieldChecks = {
      productName: frontendData?.productName === 'Test Product',
      googlePlayLink: frontendData?.googlePlayLink === 'https://play.google.com/store/apps/details?id=test.app',
      appStoreLink: frontendData?.appStoreLink === 'https://apps.apple.com/us/app/test-app/id123456789',
      appInfo: JSON.stringify(frontendData?.app_info) === JSON.stringify(backendData.app_info),
      status: frontendData?.status === 1,
    };
    
    console.log('✅ 字段检查结果:', fieldChecks);
    
    // 测试前端到后端的转换
    if (frontendData) {
      const backToBackend = productApi.transformFrontendToBackend?.(frontendData);
      console.log('🔄 转换回后端格式:', backToBackend);
      
      const backendChecks = {
        name: backToBackend?.name === 'Test Product',
        google_play_link: backToBackend?.google_play_link === 'https://play.google.com/store/apps/details?id=test.app',
        app_store_link: backToBackend?.app_store_link === 'https://apps.apple.com/us/app/test-app/id123456789',
        appInfo: JSON.stringify(backToBackend?.app_info) === JSON.stringify(backendData.app_info),
        status: backToBackend?.status === '生效',
      };
      
      console.log('✅ 后端字段检查结果:', backendChecks);
      
      const allChecksPass = Object.values(fieldChecks).every(Boolean) && 
                           Object.values(backendChecks).every(Boolean);
      
      return {
        success: allChecksPass,
        message: allChecksPass ? '字段映射测试通过' : '字段映射存在问题',
        details: {
          backendData,
          frontendData,
          backToBackend,
          fieldChecks,
          backendChecks,
        },
      };
    }
    
    return {
      success: false,
      message: '前端数据转换失败',
      details: { backendData, frontendData },
    };
  } catch (error: any) {
    console.error('❌ 字段映射测试失败:', error);
    
    return {
      success: false,
      message: `字段映射测试失败: ${error.message}`,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
    };
  }
}

/**
 * 测试app_info数据回显
 */
export async function testAppInfoEcho(): Promise<FieldMappingTestResult> {
  try {
    console.log('🧪 测试app_info数据回显...');
    
    // 模拟从后端获取的app_info数据
    const mockAppInfo = {
      'Category': 'Simulation',
      'Languages': 'English, Chinese, French, German, Italian',
      'Age Rating': '12+',
      'iOS Requires': 'iOS 13.0 or higher',
      'Android Requires': '5.0 or higher',
      'Custom Field': 'Custom Value',
    };
    
    console.log('📥 模拟app_info数据:', mockAppInfo);
    
    // 测试AppInfoEditor组件的数据处理
    // 这里模拟组件的初始化过程
    const items = Object.entries(mockAppInfo).map(([key, value], index) => ({
      id: `app_info_${Date.now()}_${index}`,
      key,
      value: value || '',
    }));
    
    console.log('🔄 转换为表格数据:', items);
    
    // 测试从表格数据转换回对象
    const backToObject: Record<string, string> = {};
    items.forEach(item => {
      if (item.key && item.key.trim()) {
        backToObject[item.key.trim()] = item.value || '';
      }
    });
    
    console.log('🔄 转换回对象格式:', backToObject);
    
    // 验证数据完整性
    const isDataIntact = JSON.stringify(mockAppInfo) === JSON.stringify(backToObject);
    
    return {
      success: isDataIntact,
      message: isDataIntact ? 'app_info数据回显测试通过' : 'app_info数据回显存在问题',
      details: {
        original: mockAppInfo,
        tableData: items,
        backToObject,
        isDataIntact,
      },
    };
  } catch (error: any) {
    console.error('❌ app_info回显测试失败:', error);
    
    return {
      success: false,
      message: `app_info回显测试失败: ${error.message}`,
      error,
    };
  }
}

/**
 * 完整的字段映射测试
 */
export async function runFullFieldMappingTest(): Promise<{
  fieldMappingTest: FieldMappingTestResult;
  appInfoEchoTest: FieldMappingTestResult;
  summary: {
    allPassed: boolean;
    passedCount: number;
    totalCount: number;
    issues: string[];
  };
}> {
  console.log('🚀 开始完整的字段映射测试...');
  
  const fieldMappingTest = await testFieldMapping();
  const appInfoEchoTest = await testAppInfoEcho();
  
  const tests = [fieldMappingTest, appInfoEchoTest];
  const passedCount = tests.filter(test => test.success).length;
  const totalCount = tests.length;
  const allPassed = passedCount === totalCount;
  
  const issues: string[] = [];
  if (!fieldMappingTest.success) issues.push('字段映射问题');
  if (!appInfoEchoTest.success) issues.push('app_info回显问题');
  
  console.log('📊 测试结果汇总:');
  console.log(`- 字段映射: ${fieldMappingTest.success ? '✅' : '❌'}`);
  console.log(`- app_info回显: ${appInfoEchoTest.success ? '✅' : '❌'}`);
  console.log(`- 总体结果: ${allPassed ? '✅ 全部通过' : `❌ ${passedCount}/${totalCount} 通过`}`);
  
  if (issues.length > 0) {
    console.log('🔧 需要修复的问题:', issues);
  }
  
  return {
    fieldMappingTest,
    appInfoEchoTest,
    summary: {
      allPassed,
      passedCount,
      totalCount,
      issues,
    },
  };
}

// 在开发环境下，将测试函数挂载到全局对象上
if (import.meta.env.DEV) {
  (window as any).fieldMappingTest = {
    testFieldMapping,
    testAppInfoEcho,
    runFullFieldMappingTest,
  };
  
  console.log('🔧 字段映射测试工具已加载，可在控制台使用:');
  console.log('- window.fieldMappingTest.runFullFieldMappingTest()');
  console.log('- window.fieldMappingTest.testFieldMapping()');
  console.log('- window.fieldMappingTest.testAppInfoEcho()');
}
