/**
 * API 接口测试工具
 * 用于快速测试后台接口连通性
 */

import { requestClient } from '#/api/request';

export class ApiTester {
  /**
   * 测试接口连通性
   */
  static async testConnection(url?: string) {
    try {
      const testUrl = url || '/health';
      const response = await requestClient.get(testUrl);
      console.log('✅ 接口连通性测试成功:', response);
      return { success: true, data: response };
    } catch (error) {
      console.error('❌ 接口连通性测试失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 测试登录接口
   */
  static async testLogin(username = 'admin', password = 'admin123') {
    try {
      const response = await requestClient.post('/auth/login', {
        username,
        password,
      });
      console.log('✅ 登录接口测试成功:', response);
      return { success: true, data: response };
    } catch (error) {
      console.error('❌ 登录接口测试失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 测试用户列表接口
   */
  static async testUserList() {
    try {
      const response = await requestClient.get('/user/list', {
        params: { page: 1, size: 10 },
      });
      console.log('✅ 用户列表接口测试成功:', response);
      return { success: true, data: response };
    } catch (error) {
      console.error('❌ 用户列表接口测试失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 测试所有核心接口
   */
  static async testAllCoreApis() {
    console.log('🚀 开始测试所有核心接口...');
    
    const results = {
      connection: await this.testConnection(),
      login: await this.testLogin(),
      userList: await this.testUserList(),
    };

    const successCount = Object.values(results).filter(r => r.success).length;
    const totalCount = Object.keys(results).length;

    console.log(`📊 测试结果: ${successCount}/${totalCount} 个接口测试通过`);
    
    if (successCount === totalCount) {
      console.log('🎉 所有接口测试通过！');
    } else {
      console.log('⚠️ 部分接口测试失败，请检查配置');
    }

    return results;
  }

  /**
   * 检查响应数据格式
   */
  static checkResponseFormat(response: any) {
    const checks = {
      hasCode: 'code' in response,
      hasData: 'data' in response,
      hasMessage: 'message' in response || 'msg' in response,
      codeIsNumber: typeof response.code === 'number',
      successCode: response.code === 0 || response.code === 200,
    };

    console.log('📋 响应格式检查:', checks);
    
    const passedChecks = Object.values(checks).filter(Boolean).length;
    const totalChecks = Object.keys(checks).length;
    
    if (passedChecks === totalChecks) {
      console.log('✅ 响应格式符合预期');
    } else {
      console.log('⚠️ 响应格式可能需要调整，请检查 adapter.ts');
    }

    return checks;
  }
}

// 在开发环境下自动暴露到全局，方便调试
if (import.meta.env.DEV) {
  (window as any).ApiTester = ApiTester;
  console.log('🔧 ApiTester 已暴露到全局，可在控制台使用 ApiTester.testAllCoreApis() 进行测试');
}
