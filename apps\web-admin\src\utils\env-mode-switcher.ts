/**
 * 环境模式切换工具
 * 用于在Mock模式和真实API模式之间切换
 */

export interface EnvModeInfo {
  mode: 'mock' | 'real';
  apiUrl: string;
  mockEnabled: boolean;
  proxyTarget: string;
  description: string;
}

/**
 * 获取当前环境模式信息
 */
export function getCurrentEnvMode(): EnvModeInfo {
  const mockEnabled = import.meta.env.VITE_NITRO_MOCK === 'true';
  const apiUrl = import.meta.env.VITE_GLOB_API_URL;
  
  return {
    mode: mockEnabled ? 'mock' : 'real',
    apiUrl,
    mockEnabled,
    proxyTarget: mockEnabled 
      ? 'http://localhost:5320/api' 
      : 'https://www.fb-hc-ads.click/mng/',
    description: mockEnabled 
      ? 'Mock模式 - 使用本地模拟数据' 
      : '真实API模式 - 连接真实后端服务',
  };
}

/**
 * 检查Mock服务是否可用
 */
export async function checkMockServiceStatus(): Promise<{
  available: boolean;
  message: string;
  error?: any;
}> {
  try {
    const response = await fetch('http://localhost:5320/api/status', {
      method: 'GET',
      timeout: 3000,
    } as any);
    
    if (response.ok) {
      return {
        available: true,
        message: 'Mock服务运行正常',
      };
    } else {
      return {
        available: false,
        message: `Mock服务响应异常: ${response.status}`,
      };
    }
  } catch (error: any) {
    return {
      available: false,
      message: 'Mock服务不可用，请启动Mock服务器',
      error: error.message,
    };
  }
}

/**
 * 检查真实API服务是否可用
 */
export async function checkRealApiStatus(): Promise<{
  available: boolean;
  message: string;
  error?: any;
}> {
  try {
    // 通过代理检查真实API状态
    const response = await fetch('/api/status', {
      method: 'GET',
      timeout: 5000,
    } as any);
    
    return {
      available: true,
      message: '真实API服务连接正常',
    };
  } catch (error: any) {
    return {
      available: false,
      message: '真实API服务连接失败',
      error: error.message,
    };
  }
}

/**
 * 获取Mock用户列表
 */
export function getMockUsers() {
  return [
    {
      username: 'vben',
      password: '123456',
      realName: 'Vben',
      roles: ['super'],
      description: '超级管理员 - 拥有所有权限',
    },
    {
      username: 'admin',
      password: '123456',
      realName: 'Admin',
      roles: ['admin'],
      description: '管理员 - 拥有管理权限',
    },
    {
      username: 'jack',
      password: '123456',
      realName: 'Jack',
      roles: ['user'],
      description: '普通用户 - 基础权限',
    },
  ];
}

/**
 * 显示环境切换指南
 */
export function showEnvSwitchGuide() {
  const currentMode = getCurrentEnvMode();
  
  console.log('🔧 环境模式切换指南');
  console.log('==================');
  console.log(`当前模式: ${currentMode.description}`);
  console.log(`API地址: ${currentMode.apiUrl}`);
  console.log(`代理目标: ${currentMode.proxyTarget}`);
  console.log('');
  
  if (currentMode.mode === 'mock') {
    console.log('📝 切换到真实API模式:');
    console.log('1. 修改 apps/web-admin/.env.development');
    console.log('2. 设置 VITE_NITRO_MOCK=false');
    console.log('3. 重启开发服务器');
    console.log('');
    console.log('🧪 Mock用户账号:');
    getMockUsers().forEach(user => {
      console.log(`- ${user.username}/${user.password} (${user.description})`);
    });
  } else {
    console.log('📝 切换到Mock模式:');
    console.log('1. 启动Mock服务器: cd apps/backend-mock && npm run dev');
    console.log('2. 修改 apps/web-admin/.env.development');
    console.log('3. 设置 VITE_NITRO_MOCK=true');
    console.log('4. 重启开发服务器');
  }
  
  console.log('');
  console.log('🔍 检查服务状态:');
  console.log('- window.envSwitcher.checkMockServiceStatus()');
  console.log('- window.envSwitcher.checkRealApiStatus()');
}

/**
 * 运行环境诊断
 */
export async function runEnvDiagnostic() {
  console.log('🔍 开始环境诊断...');
  
  const currentMode = getCurrentEnvMode();
  console.log(`当前模式: ${currentMode.description}`);
  
  // 检查Mock服务状态
  console.log('检查Mock服务状态...');
  const mockStatus = await checkMockServiceStatus();
  console.log(`Mock服务: ${mockStatus.available ? '✅' : '❌'} ${mockStatus.message}`);
  
  // 检查真实API状态
  console.log('检查真实API状态...');
  const realApiStatus = await checkRealApiStatus();
  console.log(`真实API: ${realApiStatus.available ? '✅' : '❌'} ${realApiStatus.message}`);
  
  // 提供建议
  console.log('');
  console.log('📋 诊断结果:');
  
  if (currentMode.mode === 'mock') {
    if (mockStatus.available) {
      console.log('✅ Mock模式配置正确，可以正常使用');
      console.log('🧪 可用的测试账号:');
      getMockUsers().forEach(user => {
        console.log(`   ${user.username}/${user.password}`);
      });
    } else {
      console.log('❌ Mock模式配置错误');
      console.log('💡 解决方案: 启动Mock服务器 (cd apps/backend-mock && npm run dev)');
    }
  } else {
    if (realApiStatus.available) {
      console.log('✅ 真实API模式配置正确，可以正常使用');
    } else {
      console.log('❌ 真实API连接失败');
      console.log('💡 解决方案: 检查网络连接或切换到Mock模式进行开发');
    }
  }
  
  return {
    currentMode,
    mockStatus,
    realApiStatus,
  };
}

/**
 * 创建环境配置文件内容
 */
export function generateEnvConfig(mode: 'mock' | 'real') {
  const baseConfig = `# 端口号
VITE_PORT=5777

VITE_BASE=/

# 接口地址配置`;

  if (mode === 'mock') {
    return `${baseConfig}
# Mock模式配置
VITE_GLOB_API_URL=/api
VITE_NITRO_MOCK=true

# Mock模式说明：
# - 使用本地Mock服务器提供模拟数据
# - Mock服务器地址: http://localhost:5320
# - 测试账号: vben/123456, admin/123456, jack/123456

# 是否打开 devtools
VITE_DEVTOOLS=false

# 是否注入全局loading
VITE_INJECT_APP_LOADING=true`;
  } else {
    return `${baseConfig}
# 真实API模式配置
VITE_GLOB_API_URL=/api
VITE_NITRO_MOCK=false

# 真实API模式说明：
# - 通过Vite代理连接真实后端服务
# - 代理目标: https://www.fb-hc-ads.click/mng/
# - 需要使用真实的用户名和密码登录

# 是否打开 devtools
VITE_DEVTOOLS=false

# 是否注入全局loading
VITE_INJECT_APP_LOADING=true`;
  }
}

// 在开发环境下，将工具函数挂载到全局对象上
if (import.meta.env.DEV) {
  (window as any).envSwitcher = {
    getCurrentEnvMode,
    checkMockServiceStatus,
    checkRealApiStatus,
    getMockUsers,
    showEnvSwitchGuide,
    runEnvDiagnostic,
    generateEnvConfig,
  };
  
  console.log('🔧 环境切换工具已加载，可在控制台使用:');
  console.log('- window.envSwitcher.getCurrentEnvMode()');
  console.log('- window.envSwitcher.showEnvSwitchGuide()');
  console.log('- window.envSwitcher.runEnvDiagnostic()');
  console.log('- window.envSwitcher.getMockUsers()');
}
