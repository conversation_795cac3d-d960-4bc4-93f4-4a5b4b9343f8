import type { UserInfo } from '@vben/types';

import { requestClient } from '#/api/request';

/**
 * 获取用户信息 - 已简化，不再使用profile接口
 *
 * 注意：用户信息现在直接从登录接口获取，不再需要单独的用户信息接口
 * 如果确实需要获取最新的用户信息，可以使用以下接口（但通常不需要）
 */
export async function getUserInfoApi(): Promise<UserInfo> {
  // 由于权限控制已移至后端，这个接口主要用于获取用户基本信息
  // 大多数情况下，应该使用登录时返回的用户信息
  console.warn('getUserInfoApi: 建议使用登录接口返回的用户信息，而不是单独调用此接口');

  try {
    // 如果确实需要最新用户信息，可以调用一个简单的用户信息接口
    const response = await requestClient.get('/user/info');

    return {
      userId: response.id?.toString() || '',
      username: response.username || '',
      realName: response.nickname || response.username || '',
      avatar: response.avatar || '',
      roles: [], // 不再依赖前端角色
      desc: response.status || '',
      homePath: '/dashboard',
      token: '', // token由登录接口提供
    } as UserInfo;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    throw new Error('获取用户信息失败，请重新登录');
  }
}
