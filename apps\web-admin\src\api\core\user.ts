import type { UserInfo } from '@vben/types';

import { requestClient } from '#/api/request';

/**
 * 获取用户信息 - 适配真实后端API
 */
export async function getUserInfoApi() {
  try {
    // 尝试从 /auth/profile 获取用户信息（根据真实后端接口）
    const response = await requestClient.get('/auth/profile');

    // 将真实后端返回格式转换为前端期望的UserInfo格式
    return {
      userId: response.id?.toString() || '',
      username: response.username || '',
      realName: response.nickname || response.username || '',
      avatar: response.avatar || '',
      roles: response.roles?.map((role: any) => role.name) || [],
      desc: response.status || '',
      homePath: '/dashboard',
      token: '', // token由登录接口提供
    } as UserInfo;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    // 如果 /auth/profile 失败，尝试 /user/info
    try {
      return await requestClient.get<UserInfo>('/user/info');
    } catch (fallbackError) {
      console.error('备用用户信息接口也失败:', fallbackError);
      throw error;
    }
  }
}
